# Subnet-NSG Associations

# Bastion Subnet Association
resource "azurerm_subnet_network_security_group_association" "bastion_nsg_association" {
  subnet_id                 = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/AzureBastionSubnet"
  network_security_group_id = module.nsg_bastion.nsg_id
}

# CloudPCs Test Subnet Association
resource "azurerm_subnet_network_security_group_association" "cloudpcs_test_nsg_association" {
  subnet_id                 = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/snet-cloudpcs-test"
  network_security_group_id = module.nsg_cloudpcs_test.nsg_id
}

# DNS Inbound Subnet Association
resource "azurerm_subnet_network_security_group_association" "dns_inbound_nsg_association" {
  subnet_id                 = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/snet-dns-inbound"
  network_security_group_id = module.nsg_dns_inbound.nsg_id
}

# DNS Outbound Subnet Association
resource "azurerm_subnet_network_security_group_association" "dns_outbound_nsg_association" {
  subnet_id                 = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/snet-dns-outbound"
  network_security_group_id = module.nsg_dns_outbound.nsg_id
}

# Identity DC Subnet Association
resource "azurerm_subnet_network_security_group_association" "identity_dc_nsg_association" {
  subnet_id                 = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/snet-identity-dc"
  network_security_group_id = module.nsg_identity_dc.nsg_id
}

# Private Link Storage Subnet Association
resource "azurerm_subnet_network_security_group_association" "privatelink_storage_nsg_association" {
  subnet_id                 = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/snet-privatelink-storage"
  network_security_group_id = module.nsg_privatelink_storage.nsg_id
}
