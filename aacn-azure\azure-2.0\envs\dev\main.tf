provider "azurerm" {
  features {}
}


module "mgmt_groups" {
  source                                      = "../../modules/mgmt_groups"
  root_management_group_name                  = var.root_management_group_name
  root_management_group_display_name          = var.root_management_group_display_name
  landing_zones_management_group_name         = var.landing_zones_management_group_name
  landing_zones_management_group_display_name = var.landing_zones_management_group_display_name
  platform_management_group_name              = var.platform_management_group_name
  platform_management_group_display_name      = var.platform_management_group_display_name
  sandbox_management_group_name               = var.sandbox_management_group_name
  sandbox_management_group_display_name       = var.sandbox_management_group_display_name
  # subscription_id                             = var.subscription_id
  sandbox_subscription_id      = toset([var.sandbox_subscription_id])
  platform_subscription_id     = toset([var.platform_subscription_id])
  landing_zone_subscription_id = toset([var.landing_zone_subscription_id])
}