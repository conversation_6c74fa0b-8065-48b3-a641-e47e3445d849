data "terraform_remote_state" "spoke1" {
  backend = "azurerm"
  config = {
    storage_account_name = "stprodterra001"  # Replace with your storage account name
    container_name       = "tfstate"               # Replace with your container name
    key                  = "prod.terraform.tfstate" # Path to your state file for Spoke1 VNet
    resource_group_name  = "rg-production-mgmt-westus2-001"             # Replace with the resource group for the storage account
    subscription_id      = "21dc48e9-b2ef-4158-bc7b-7af56a4df1cb"   # Spoke1 subscription ID
  }
}
resource "azurerm_virtual_network_peering" "hub_to_spoke1" {
  name                      = "hub-to-prod"
  resource_group_name       = var.resource_group_name
  virtual_network_name      = "vnet-platform-hub-westus2-001"
  remote_virtual_network_id = data.terraform_remote_state.spoke1.outputs.spoke1_vnet_id
  allow_forwarded_traffic   = true
  allow_gateway_transit     = false
  use_remote_gateways       = false
}

resource "azurerm_virtual_network_peering" "hub_to_iland_spoke" {
  name                      = "hub_to_iland_spoke"
  resource_group_name       = var.resource_group_name
  virtual_network_name      = "vnet-platform-hub-westus2-001"
  remote_virtual_network_id = "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-connectivity-prod-001/providers/Microsoft.Network/virtualNetworks/vnet-production-iland-westus2-001"
  allow_forwarded_traffic   = true
  allow_gateway_transit     = false
  use_remote_gateways       = false
}

data "terraform_remote_state" "spoke2" {
  backend = "azurerm"
  config = {
    storage_account_name = "stdevtestterra001"  # Replace with your storage account name
    container_name       = "tfstate"               # Replace with your container name
    key                  = "dev.terraform.tfstate" # Path to your state file for Spoke2 VNet
    resource_group_name  = "rg-devtest-mgmt-westus2-001"             # Replace with the resource group for the storage account
    subscription_id      = "067e726d-1a79-4cc3-aba1-7fb1398638b5"      # Spoke2 subscription ID
  }
}
resource "azurerm_virtual_network_peering" "hub_to_spoke2" {
  name                      = "hub-to-dev"
  resource_group_name       = var.resource_group_name
  virtual_network_name      = "vnet-platform-hub-westus2-001"
  remote_virtual_network_id = data.terraform_remote_state.spoke2.outputs.spoke2_vnet_id
  allow_forwarded_traffic   = true
  allow_gateway_transit     = false
  use_remote_gateways       = false
}

