# variable "resource_group_name" {
#   default = "vm-platform-testvm-001"
# }

# variable "location" {
#   default = "West US 2"
# }

variable "subscription_id" {
  default = "fce95a24-209c-4e69-98de-885660170e1d"
}

# variable "resource_group_tags" {
#   type = map(string)
#   default = {
#     environment = "Platform"
#     CostCenter  = "1911-51210"
#   }
# }

# variable "admin_password" {
#   default = "Aacn@*********"
# }