module "aadds_network" {
  source = "../../../../../modules/networking/aadds"

  resource_group_name = "rg-platform-identity-westus2-001"
  location            = "westus2"
  
  # Load Balancer and Public IP
  lb_name         = "aadds-bfdf416bd26b4958a920d51094d43f5e-lb"
  public_ip_name  = "aadds-bfdf416bd26b4958a920d51094d43f5e-pip"
  domain_name_label = "cust-a-a-c-n-0-o-r-g-d4d95bee-ce46-4803-b7e2-63d06658ef0b"
  
  # Network Interfaces
  nic_name_1      = "aadds-82010bb7eb544148ab08871742852500-nic"
  nic_name_2      = "aadds-cef2c9ec4658447bad45f74ce45ef0fe-nic"
  dns_servers     = ["********", "********"]
  
  # Subnet reference
  subnet_id       = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/snet-identity-dc"
  
  # NAT rule configuration
  name_prefix     = "LK-JWW7L0ZFMZB4"
  nat_rule_prefix_1 = "GPXUMNTT4QWKXBVP"
  nat_rule_prefix_2 = "W-93GOMVYO3AONG"
  ip_config_prefix_1 = "GPXUMNTT4QWKXBV"
  ip_config_prefix_2 = "W-93GOMVYO3AONG"
  frontend_port_1 = 5986
  frontend_port_2 = 5987
  backend_port    = 5986
  
  tags = {
    Environment = "Platform"
    Service     = "AADDS"
    Terraform   = "true"
  }
}

# Outputs
output "aadds_public_ip" {
  value = module.aadds_network.public_ip_address
}

output "aadds_nic_1_private_ip" {
  value = module.aadds_network.nic_1_private_ip
}

output "aadds_nic_2_private_ip" {
  value = module.aadds_network.nic_2_private_ip
}