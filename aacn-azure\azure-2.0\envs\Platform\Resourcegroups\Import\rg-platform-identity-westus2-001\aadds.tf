module "aadds_network" {
  source = "../../../../../modules/networking/aadds"

  resource_group_name = "rg-platform-identity-westus2-001"
  location            = "westus2"
  
  # Load Balancer and Public IP
  lb_name         = "aadds-bfdf416bd26b4958a920d51094d43f5e-lb"
  public_ip_name  = "aadds-bfdf416bd26b4958a920d51094d43f5e-pip"
  domain_name_label = "cust-a-z-u-r-e-0-a-a-c-n-0-bfdf416b-d26b-4958-a920-d51094d43f5e"
  
  # Network Interfaces
  nic_name_1      = "aadds-82010bb7eb544148ab08871742852500-nic"
  nic_name_2      = "aadds-cef2c9ec4658447bad45f74ce45ef0fe-nic"
  dns_servers     = ["********", "********"]
  
  # Subnet reference
  subnet_id       = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/snet-identity-dc"
  
  # NAT rule configuration
  name_prefix     = "XVTJGKJTOAYUW-AFe"
  nat_rule_prefix_1 = "PZ6-W7IOKKBAWG6Psh"
  nat_rule_prefix_2 = "TG1HGIBHAOX0OAKPsh"
  ip_config_prefix_1 = "PZ6-W7IOKKBAWG6Ipcfg"
  ip_config_prefix_2 = "TG1HGIBHAOX0OAKIpcfg"
  frontend_port_1 = 5986
  frontend_port_2 = 5987
  backend_port    = 5986
  
  tags = {
    Environment = "Platform"
    Service     = "AADDS"
    Terraform   = "true"
  }
}

# Outputs
output "aadds_public_ip" {
  value = module.aadds_network.public_ip_address
}

output "aadds_nic_1_private_ip" {
  value = module.aadds_network.nic_1_private_ip
}

output "aadds_nic_2_private_ip" {
  value = module.aadds_network.nic_2_private_ip
}