module "spoke2_vnet" {
  source = "../../../../modules/networking/spoke_vnet"

  vnet_name              = "vnet-westus2-devtest-dev"
  address_space          = ["*********/16"]
  location               = var.location
  resource_group_name    = var.resource_group_name
  subnet_name            = "snet-devtest-PRODUCT-dev"
  subnet_address_prefixes = ["*********/24"]

  providers = {
    azurerm = azurerm.devtest
  }
} 

#Outputs
output "spoke2_vnet_name" {
  value = module.spoke2_vnet.vnet_name
}

output "spoke2_vnet_id" {
  value = module.spoke2_vnet.vnet_id
}

output "spoke2_subnet_id" {
  value = module.spoke2_vnet.subnet_id
}