{"version": 3, "serial": 1, "lineage": "4a55214c-28bf-5361-0f44-b912bf486e94", "backend": {"type": "azurerm", "config": {"access_key": null, "client_certificate_password": null, "client_certificate_path": null, "client_id": null, "client_secret": null, "container_name": "tfstate", "endpoint": null, "environment": null, "key": "prod.terraform.tfstate", "metadata_host": null, "msi_endpoint": null, "oidc_request_token": null, "oidc_request_url": null, "oidc_token": null, "oidc_token_file_path": null, "resource_group_name": "rg-production-mgmt-westus2-001", "sas_token": null, "snapshot": null, "storage_account_name": "stprodterra001", "subscription_id": null, "tenant_id": null, "use_azuread_auth": null, "use_msi": null, "use_oidc": null}, "hash": **********}, "modules": [{"path": ["root"], "outputs": {}, "resources": {}, "depends_on": []}]}