module "application_insights" {
  source = "../../../../modules/application_insights"

  app_insights_config = {
    for config in var.app_insights_list : config.name => {
      name                = config.name
      location            = var.location
      resource_group_name = var.resource_group_name
      application_type    = config.application_type
      workspace_id        = azurerm_log_analytics_workspace.log_analytics.id
      tags                = config.tags
    }
  }
}

