# resource "azurerm_windows_virtual_machine" "res-1" {
#   admin_password        = "P@$$w0rd1234!"
#   admin_username        = "aacn"
#   license_type          = "Windows_Client"
#   location              = "westus2"
#   name                  = "vm-aacn-prod-001"
#   network_interface_ids = ["/subscriptions/7daa1d35-dc46-4683-9c8c-b175bf57817a/resourceGroups/rg-westus2-network-prod-001/providers/Microsoft.Network/networkInterfaces/vm-aacn-prod-001168"]
#   resource_group_name   = "rg-westus2-network-prod-001"
#   size                  = "Standard_B2s"
#   tags = {
#     CostCenter = "1911-51210"
#   }
#   boot_diagnostics {
#   }
#   os_disk {
#     caching              = "ReadWrite"
#     storage_account_type = "StandardSSD_LRS"
#   }
#   source_image_reference {
#     offer     = "windows-11"
#     publisher = "microsoftwindowsdesktop"
#     sku       = "win11-22h2-pro"
#     version   = "latest"
#   }
#   depends_on = [
#     azurerm_network_interface.res-4,
#   ]
# }