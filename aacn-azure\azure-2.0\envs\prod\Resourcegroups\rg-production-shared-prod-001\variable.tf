variable "resource_group_name" {
  default = "rg-production-shared-001"
}

variable "location" {
  default = "West US 2"
}

variable "subscription_id" {
  default = "21dc48e9-b2ef-4158-bc7b-7af56a4df1cb"
}

variable "tenant_id" {
  default = "ca4fd2d7-85ea-42d2-b7b3-30ef2666c7ab"
}

variable "resource_group_tags" {
  type = map(string)
  default = {
    environment = "prod"
    # CostCenter = "1911-51210"
  }
}
variable "key_vault_names" {
  description = "List of Key Vault names to be created"
  type        = list(string)
  default     = [
    "kv-production-cert-001",
    "kv-production-member-001",
    "kv-production-NE-001",
    "kv-production-NTI-001",
    "kv-production-LE-001",
    "kv-production-chapt-001",
  ]
}


variable "sku" {
  default = "standard"
}

## Key vaults Variable
variable "key_vaults_tags" {
  type = map(string)
  default = {
    environment = "Platform"
    # CostCenter  = "1911-51210"
  }
}

# Object IDs for access policies
# variable "devops_leads_object_id" {
#   type        = string
#   description = "Object ID for the DevOps Leads group"
#   default = "5866b75a-2207-4212-ad24-3b725610b2a1"
# }

# variable "dynatech_engineers_object_id" {
#   type        = string
#   description = "Object ID for the DynaTech Engineers group"
#   default = "485da724-018a-4d86-8aee-adf46326de2f"
# }

# variable "aacn_dps_it_operations_object_id" {
#   type        = string
#   description = "Object ID for the AACN-DPS-IT Operations application"
#   default = "45f280a8-4460-4ac1-bcbd-ca26d65a679e"
# }

variable "access_policy_object_ids" {
  description = "List of object IDs for access policies"
  type        = list(string)
  default     = [
    "5866b75a-2207-4212-ad24-3b725610b2a1",
    "485da724-018a-4d86-8aee-adf46326de2f",
    "45f280a8-4460-4ac1-bcbd-ca26d65a679e",
    "0c6c6f5a-9f1c-4a56-8e4f-fe94df081d01",
  ]
}

variable "secret_permissions" {
  description = "Default secret permissions for Key Vault access policy"
  type        = list(string)
  default     = ["Get", "List" , "Delete", "Recover", "Backup", "Restore", "Purge", "Set"]
}

variable "key_permissions" {
  description = "Default key permissions for Key Vault access policy"
  type        = list(string)
  default     = ["Get", "List" , "Update", "Create", "Import", "Delete", "Recover", "Backup", "Restore", "Decrypt", "Encrypt", "Purge", "Sign", "UnwrapKey", "Verify", "WrapKey"]
}

variable "certificate_permissions" {
  description = "Default certificate permissions for Key Vault access policy"
  type        = list(string)
  default     = ["Get", "List" , "Update", "Create", "Import", "Delete", "Recover", "Backup", "Restore", "DeleteIssuers", "GetIssuers", "ListIssuers", "ManageContacts", "ManageIssuers", "SetIssuers"]
}

# App configuration variable 

variable "app_configuration_list" {
  description = "List of app configuration settings"
  type = list(object({
    name                = string
    sku                 = string
    enabled             = bool
    tags                = map(string)
  }))

  default = [
    {
      name    = "appcs-production-cert-001"
      sku     = "standard"
      enabled = true
      tags    = { environment = "prod" }
    },
    {
      name    = "appcs-production-member-001"
      sku     = "standard"
      enabled = true
      tags    = { environment = "prod" }
    },
    {
      name    = "appcs-production-NE-001"
      sku     = "standard"
      enabled = true
      tags    = { environment = "prod" }
    },
    {
      name    = "appcs-production-NTI-001"
      sku     = "standard"
      enabled = true
      tags    = { environment = "prod" }
    },
    {
      name    = "appcs-production-LE-001"
      sku     = "standard"
      enabled = true
      tags    = { environment = "prod" }
    },
    {
      name    = "appcs-production-chapt-001"
      sku     = "standard"
      enabled = true
      tags    = { environment = "prod" }
    }
  ]
}

# App Insight Variable 

variable "app_insights_list" {
  description = "List of Application Insights configurations"
  type = list(object({
    name             = string
    application_type = string
    # workspace_id     = optional(string, null)
    tags             = map(string)
  }))

  default = [
    {
      name             = "appi-production-cert-001"
      application_type = "web"
      tags             = { environment = "prod" }
    },
    {
      name             = "appi-production-member-001"
      application_type = "web"
      tags             = { environment = "prod" }
    },
    {
      name             = "appi-production-NE-001"
      application_type = "web"
      tags             = { environment = "prod" }
    },
    {
      name             = "appi-production-NTI-001"
      application_type = "web"
      tags             = { environment = "prod" }
    },
    {
      name             = "appi-production-LE-001"
      application_type = "web"
      tags             = { environment = "prod" }
    },
    {
      name             = "appi-production-chapters-001"
      application_type = "web"
      tags             = { environment = "prod" }
    }
  ]
}

variable "log_analytics_workspace_name" {
  default = "work-production-shared-001"
}

variable "log_analytics_workspace_sku" {
  default = "PerGB2018"
}

variable "log_analytics_retention_in_days" {
  default = "30"
}

variable "log_analytics_tags" {
  type = map(string)
  default = {
    Environment = "dev"
    CostCenter = "1911-51210"
  }
}
