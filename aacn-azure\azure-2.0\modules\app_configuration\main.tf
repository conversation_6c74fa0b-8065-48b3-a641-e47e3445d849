resource "azurerm_app_configuration" "app_config" {
  # Create only the App Configurations with `enabled = true`
  for_each = { for k, v in var.app_configurations : k => v if v.enabled }

  name                = each.value.name
  location            = each.value.location
  resource_group_name = each.value.resource_group_name
  sku                 = each.value.sku
  tags                = each.value.tags
}
