module "frontdoor" {
  source               = "../../../../../modules/frontdoor"
  frontdoor_name       = var.frontdoor_name
  resource_group_name  = var.resource_group_name
  frontend_endpoint_name = var.frontend_endpoint_name
  frontend_host_name   = var.frontend_host_name
  backend_pool_name    = var.backend_pool_name
  backend_host         = var.backend_host
  backend_address      = var.backend_address
  backend_http_port    = var.backend_http_port
  backend_https_port   = var.backend_https_port
  load_balancing_name  = var.load_balancing_name
  health_probe_name    = var.health_probe_name
  patterns_to_match    = var.patterns_to_match
  accepted_protocols   = var.accepted_protocols
}