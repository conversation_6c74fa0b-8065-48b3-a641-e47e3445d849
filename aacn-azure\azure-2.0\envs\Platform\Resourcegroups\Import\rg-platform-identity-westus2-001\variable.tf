variable "resource_group_name" {
  default = "rg-platform-identity-westus2-001"
}

variable "location" {
  default = "West US 2"
}

variable "resource_group_tags" {
  type = map(string)
  default = {
    CostCenter  = "1911-51210"
    Environment = "Development"
  }
}

variable "recource_group_id" {
  type    = string
  default = "/subscriptions/7daa1d35-dc46-4683-9c8c-b175bf57817a/resourceGroups/rg-westus2-mgmt-dev-001"
}

variable "subscription_id" {
  default = "fce95a24-209c-4e69-98de-885660170e1d"
}

variable "tenant_id" {
  default = "ca4fd2d7-85ea-42d2-b7b3-30ef2666c7ab"
}

variable "sku" {
  default = "standard"
}

variable "key_vaults_tags" {
  type = map(string)
  default = {
    environment = "Platform"
    # CostCenter  = "1911-51210"
  }
}

variable "access_policy_object_ids" {
  description = "List of object IDs for access policies"
  type        = list(string)
  default     = [
    "5866b75a-2207-4212-ad24-3b725610b2a1",
    "485da724-018a-4d86-8aee-adf46326de2f",
    "45f280a8-4460-4ac1-bcbd-ca26d65a679e",
  ]
}

variable "secret_permissions" {
  description = "Default secret permissions for Key Vault access policy"
  type        = list(string)
  default     = ["Get", "List" , "Delete", "Recover", "Backup", "Restore", "Purge", "Set"]
}

variable "key_permissions" {
  description = "Default key permissions for Key Vault access policy"
  type        = list(string)
  default     = ["Get", "List" , "Update", "Create", "Import", "Delete", "Recover", "Backup", "Restore", "Decrypt", "Encrypt", "Purge", "Sign", "UnwrapKey", "Verify", "WrapKey"]
}

variable "certificate_permissions" {
  description = "Default certificate permissions for Key Vault access policy"
  type        = list(string)
  default     = ["Get", "List" , "Update", "Create", "Import", "Delete", "Recover", "Backup", "Restore", "DeleteIssuers", "GetIssuers", "ListIssuers", "ManageContacts", "ManageIssuers", "SetIssuers"]
}