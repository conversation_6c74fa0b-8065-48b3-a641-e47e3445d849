# Azure AD Domain Services Network Resources Module

# Load Balancer
resource "azurerm_public_ip" "aadds_pip" {
  name                = var.public_ip_name
  location            = var.location
  resource_group_name = var.resource_group_name
  allocation_method   = "Static"
  sku                 = "Standard"
  domain_name_label   = var.domain_name_label
  tags                = var.tags
}

resource "azurerm_lb" "aadds_lb" {
  name                = var.lb_name
  location            = var.location
  resource_group_name = var.resource_group_name
  sku                 = "Standard"
  tags                = var.tags

  frontend_ip_configuration {
    name                 = "${var.name_prefix}Fe"
    public_ip_address_id = azurerm_public_ip.aadds_pip.id
  }
}

# Backend Address Pool
resource "azurerm_lb_backend_address_pool" "aadds_backend_pool" {
  loadbalancer_id = azurerm_lb.aadds_lb.id
  name            = "${var.name_prefix}Be"
}

# NAT Rules
resource "azurerm_lb_nat_rule" "aadds_nat_rule_1" {
  name                           = "${var.nat_rule_prefix_1}sh"
  resource_group_name            = var.resource_group_name
  loadbalancer_id                = azurerm_lb.aadds_lb.id
  protocol                       = "Tcp"
  frontend_port                  = var.frontend_port_1
  backend_port                   = var.backend_port
  frontend_ip_configuration_name = "${var.name_prefix}Fe"
  idle_timeout_in_minutes        = 15
}

resource "azurerm_lb_nat_rule" "aadds_nat_rule_2" {
  name                           = "${var.nat_rule_prefix_2}sh"
  resource_group_name            = var.resource_group_name
  loadbalancer_id                = azurerm_lb.aadds_lb.id
  protocol                       = "Tcp"
  frontend_port                  = var.frontend_port_2
  backend_port                   = var.backend_port
  frontend_ip_configuration_name = "${var.name_prefix}Fe"
  idle_timeout_in_minutes        = 15
}

# Network Interfaces
resource "azurerm_network_interface" "aadds_nic_1" {
  name                = var.nic_name_1
  location            = var.location
  resource_group_name = var.resource_group_name
  dns_servers         = var.dns_servers
  tags                = var.tags

  ip_configuration {
    name                          = "${var.ip_config_prefix_1}Ipcfg"
    subnet_id                     = var.subnet_id
    private_ip_address_allocation = "Dynamic"
  }
}

resource "azurerm_network_interface" "aadds_nic_2" {
  name                = var.nic_name_2
  location            = var.location
  resource_group_name = var.resource_group_name
  dns_servers         = var.dns_servers
  tags                = var.tags

  ip_configuration {
    name                          = "${var.ip_config_prefix_2}Ipcfg"
    subnet_id                     = var.subnet_id
    private_ip_address_allocation = "Dynamic"
  }
}

# Network Interface Associations
resource "azurerm_network_interface_backend_address_pool_association" "aadds_nic_1_pool" {
  network_interface_id    = azurerm_network_interface.aadds_nic_1.id
  ip_configuration_name   = "${var.ip_config_prefix_1}Ipcfg"
  backend_address_pool_id = azurerm_lb_backend_address_pool.aadds_backend_pool.id
}

resource "azurerm_network_interface_nat_rule_association" "aadds_nic_1_nat" {
  network_interface_id  = azurerm_network_interface.aadds_nic_1.id
  ip_configuration_name = "${var.ip_config_prefix_1}Ipcfg"
  nat_rule_id           = azurerm_lb_nat_rule.aadds_nat_rule_1.id
}

resource "azurerm_network_interface_backend_address_pool_association" "aadds_nic_2_pool" {
  network_interface_id    = azurerm_network_interface.aadds_nic_2.id
  ip_configuration_name   = "${var.ip_config_prefix_2}Ipcfg"
  backend_address_pool_id = azurerm_lb_backend_address_pool.aadds_backend_pool.id
}

resource "azurerm_network_interface_nat_rule_association" "aadds_nic_2_nat" {
  network_interface_id  = azurerm_network_interface.aadds_nic_2.id
  ip_configuration_name = "${var.ip_config_prefix_2}Ipcfg"
  nat_rule_id           = azurerm_lb_nat_rule.aadds_nat_rule_2.id
}