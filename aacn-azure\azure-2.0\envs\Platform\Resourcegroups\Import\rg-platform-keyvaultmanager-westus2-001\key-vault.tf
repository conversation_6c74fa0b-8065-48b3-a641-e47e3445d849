# resource "azurerm_key_vault" "res-1" {
#   enabled_for_deployment          = true
#   enabled_for_disk_encryption     = true
#   enabled_for_template_deployment = true
#   location                        = "westus2"
#   name                            = "kv-westus2-mgmt-prod-001"
#   resource_group_name             = "rg-westus2-mgmt-prod-001"
#   sku_name                        = "premium"
#   tags = {
#     CostCenter  = "1911-51210"
#     displayName = "KeyVault"
#   }
#   tenant_id = "ca4fd2d7-85ea-42d2-b7b3-30ef2666c7ab"
#   depends_on = [
#     module.rg-westus2-mgmt-prod-001,
#   ]
# }

# resource "azurerm_key_vault_certificate" "res-2" {
#   key_vault_id = "/subscriptions/7daa1d35-dc46-4683-9c8c-b175bf57817a/resourceGroups/rg-westus2-mgmt-prod-001/providers/Microsoft.KeyVault/vaults/kv-westus2-mgmt-prod-001"
#   name         = "aacnorg"
#   certificate_policy {
#     issuer_parameters {
#       name = "Unknown"
#     }
#     key_properties {
#       exportable = true
#       key_type   = "RSA"
#       reuse_key  = false
#     }
#     lifetime_action {
#       action {
#         action_type = "EmailContacts"
#       }
#       trigger {
#         lifetime_percentage = 80
#       }
#     }
#     secret_properties {
#       content_type = "application/x-pkcs12"
#     }
#   }
#   depends_on = [
#     azurerm_key_vault.res-1,
#   ]
# }
# resource "azurerm_key_vault_key" "res-3" {
#   key_opts     = ["sign", "verify", "wrapKey", "unwrapKey", "encrypt", "decrypt"]
#   key_size     = 4096
#   key_type     = "RSA"
#   key_vault_id = "/subscriptions/7daa1d35-dc46-4683-9c8c-b175bf57817a/resourceGroups/rg-westus2-mgmt-prod-001/providers/Microsoft.KeyVault/vaults/kv-westus2-mgmt-prod-001"
#   name         = "test-rsa-dev-001"
#   depends_on = [
#     azurerm_key_vault.res-1,
#   ]
# }
# resource "azurerm_key_vault_secret" "res-4" {
#   expiration_date = "2025-04-30T10:52:42Z"
#   key_vault_id    = "/subscriptions/7daa1d35-dc46-4683-9c8c-b175bf57817a/resourceGroups/rg-westus2-mgmt-prod-001/providers/Microsoft.KeyVault/vaults/kv-westus2-mgmt-prod-001"
#   name            = "AACNPOC"
#   not_before_date = "2023-04-18T10:52:42Z"
#   value           = "fgfladkfwhf1243"
#   depends_on = [
#     azurerm_key_vault.res-1,
#   ]
# }
# resource "azurerm_key_vault_secret" "res-5" {
#   expiration_date = "2025-06-21T19:20:36Z"
#   key_vault_id    = "/subscriptions/7daa1d35-dc46-4683-9c8c-b175bf57817a/resourceGroups/rg-westus2-mgmt-prod-001/providers/Microsoft.KeyVault/vaults/kv-westus2-mgmt-prod-001"
#   name            = "api-events-dev-2023-JunoApiKey"
#   not_before_date = "2023-06-21T19:20:36Z"
#   tags = {
#     Environment = "Development"
#   }
#   value = "8e234d6e0fb64bf6a6b222c607fa0c62"
#   depends_on = [
#     azurerm_key_vault.res-1,
#   ]
# }

# module "stkeyvaultmanager001" {
#   source              = "../../../../../modules/key_vaults"
#   key_vaultname       = var.key_vaultname
#   location            = var.location
#   resource_group_name = var.resource_group_name
#   tenant_id           = var.tenant_id
#   sku                 = var.sku
#   key_vaults_tags     = var.key_vaults_tags
# }
