trigger:
  batch: false
  branches:
    include:
    - 'main'
  paths:
    include:
    - .azure-devops/aacn-azure-v2-iac
    - azure-2.0/**

pr:
  branches:
    exclude:
    - '*'

variables:
  - group: 'Azure2.0'
 
parameters:
  - name: environment
    displayName: Environment
    type: string
    default: dev
    values:
      - dev
      - prod
      - Platform

  - name: command
    displayName: Terraform Command
    type: string
    default: plan
    values:
      - plan
      - apply
      - destroy
  
  # - name: autoApprove
  #   displayName: Auto Approve
  #   type: boolean
  #   default: false    

stages:
  - stage: Terraform
    jobs:
      - job: TerraformJob
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: UsePythonVersion@0
            inputs:
              versionSpec: '3.x'
              addToPath: true

          - task: TerraformInstaller@1
            inputs:
              terraformVersion: 'latest'

          - task: AzureCLI@2
            inputs:
              azureSubscription: 'Azure2.0_test'
              scriptType: 'bash'
              scriptLocation: 'scriptPath'
              scriptPath: './azure-2.0/scripts/run_pipeline.sh'
              arguments: '-e ${{ parameters.environment }} -b $(basePath) ${{ parameters.command }}'
              addSpnToEnvironment: true
            # env:
            #   ARM_CLIENT_ID: $(ARM_CLIENT_ID)
            #   ARM_CLIENT_SECRET: $(ARM_CLIENT_SECRET)
            #   ARM_TENANT_ID: $(ARM_TENANT_ID)
            #   ARM_SUBSCRIPTION_ID: $(ARM_SUBSCRIPTION_ID)                