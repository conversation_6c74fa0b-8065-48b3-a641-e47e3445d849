resource "azurerm_application_insights" "app_insights" {
  for_each            = var.app_insights_config
  name                = each.value["name"]
  location            = each.value["location"]
  resource_group_name = each.value["resource_group_name"]
  application_type    = each.value["application_type"]
  # Conditionally assign workspace_id only if it's provided
  workspace_id        = try(each.value.workspace_id, null)
  tags                = each.value["tags"]
}