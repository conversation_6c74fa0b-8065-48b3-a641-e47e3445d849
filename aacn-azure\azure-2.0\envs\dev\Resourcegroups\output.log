Acquiring state lock. This may take a few moments...
[0m[1mmodule.rg-devtest-connectivity-001.data.terraform_remote_state.hub: Reading...[0m[0m
[0m[1mmodule.rg-devtest-aacnorg-dev-001.module.rg-devtest-aacnorg-dev-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-aacnorg-dev-001][0m
[0m[1mmodule.rg-devtest-learning-courses-001.module.rg-devtest-learning-courses-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-learning-courses-001][0m
[0m[1mmodule.rg-devtest-nursing-beaconsurvey-001.module.rg-devtest-nursing-beaconsurvey-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-nursing-beaconsurvey-001][0m
[0m[1mmodule.rg-devtest-connectivity-001.module.spoke2_vnet.azurerm_virtual_network.vnet: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-connectivity-001/providers/Microsoft.Network/virtualNetworks/vnet-westus2-devtest-dev][0m
[0m[1mmodule.rg-devtest-lighthouse-DW-001.module.rg-devtest-lighthouse-DW-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-lighthouse-DW-001][0m
[0m[1mmodule.rg-devtest-connectivity-001.module.rg-devtest-connectivity-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-connectivity-001][0m
[0m[1mmodule.rg-devtest-lighthouse-fabric-001.module.rg-devtest-lighthouse-fabric-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-lighthouse-fabric-001][0m
[0m[1mmodule.rg-devtest-cert-cequeue-001.module.rg-devtest-cert-cequeue-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-cert-cequeue-001][0m
[0m[1mmodule.rg-devetst-mgmt-westus2-001.module.rg-devetst-mgmt-westus2-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-mgmt-westus2-001][0m
[0m[1mmodule.rg-devetst-mgmt-westus2-001.module.dev_storage.azurerm_storage_account.storage: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-mgmt-westus2-001/providers/Microsoft.Storage/storageAccounts/stdevtestterra001][0m
[0m[1mmodule.rg-devtest-nursing-beaconv3-001.module.rg-devtest-nursing-beaconv3-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-nursing-beaconv3-001][0m
[0m[1mmodule.rg-devtest-IIS-dev-001.module.rg-devtest-IIS-dev-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-IIS-dev-001][0m
[0m[1mmodule.rg-devtest-netforum-dev-001.module.rg-devtest-netforum-dev-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-netforum-dev-001][0m
[0m[1mmodule.rg-devtest-hwe-hweatteams-001.module.rg-devtest-hwe-hweatteams-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-hwe-hweatteams-001][0m
[0m[1mmodule.rg-devtest-nti-programapproval-001.module.rg-devtest-nti-programapproval-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-nti-programapproval-001][0m
[0m[1mmodule.rg-devtest-connectivity-001.module.spoke2_vnet.azurerm_subnet.subnet: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-connectivity-001/providers/Microsoft.Network/virtualNetworks/vnet-westus2-devtest-dev/subnets/snet-devtest-PRODUCT-dev][0m
[0m[1mmodule.rg-devtest-nti-eventsv3-001.module.rg-devtest-nti-eventsv3-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-nti-eventsv3-001][0m
[0m[1mmodule.rg-devtest-cert-certtestcenter-001.module.rg-devtest-cert-certtestcenter-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-cert-certtestcenter-001][0m
[0m[1mmodule.rg-devtest-chapters-nursingnerwork-001.module.rg-devtest-chapters-nursingnerwork-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-chapters-nursingnerwork-001][0m
[0m[1mmodule.rg-devtest-assessment-dev-001.module.rg-devtest-assessment-dev-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-assessment-dev-001][0m
[0m[1mmodule.rg-devtest-connectivity-001.data.terraform_remote_state.hub: Still reading... [10s elapsed][0m[0m
[0m[1mmodule.rg-devtest-lighthouse-taxonomy-001.module.rg-devtest-lighthouse-taxonomy-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-lighthouse-taxonomy-001][0m
[0m[1mmodule.rg-devtest-taxonomy-dev-001.module.rg-devtest-taxonomy-dev-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-taxonomy-dev-001][0m
[0m[1mmodule.rg-devetst-mgmt-westus2-001.module.dev_storage.azurerm_storage_container.tfstate: Refreshing state... [id=https://stdevtestterra001.blob.core.windows.net/tfstate][0m
[0m[1mmodule.rg-devtest-connectivity-001.data.terraform_remote_state.hub: Read complete after 18s[0m
[0m[1mmodule.rg-devtest-connectivity-001.azurerm_virtual_network_peering.spoke2_to_hub: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-connectivity-001/providers/Microsoft.Network/virtualNetworks/vnet-westus2-devtest-dev/virtualNetworkPeerings/dev-to-hub][0m
[0m[1mmodule.rg-devtest-online-cert-shared-001.module.rg-devtest-online-cert-shared-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-online-cert-shared-001][0m

Terraform used the selected providers to generate the following execution
plan. Resource actions are indicated with the following symbols:
  [32m+[0m create[0m
  [31m-[0m destroy[0m

Terraform will perform the following actions:

[1m  # module.rg-devtest-assessment-dev-001.module.rg-devtest-assessment-dev-001.azurerm_resource_group.rg[0m will be [1m[31mdestroyed[0m
  # (because azurerm_resource_group.rg is not in configuration)
[0m  [31m-[0m[0m resource "azurerm_resource_group" "rg" {
      [31m-[0m[0m id       = "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-assessment-dev-001" [90m-> null[0m[0m
      [31m-[0m[0m location = "westus2" [90m-> null[0m[0m
      [31m-[0m[0m name     = "rg-devtest-assessment-dev-001" [90m-> null[0m[0m
      [31m-[0m[0m tags     = {
          [31m-[0m[0m "environment" = "devtest"
        } [90m-> null[0m[0m
    }

[1m  # module.rg-devtest-assessment-dev-001.module.rg-devtest-assessments-dev-001.azurerm_resource_group.rg[0m will be created
[0m  [32m+[0m[0m resource "azurerm_resource_group" "rg" {
      [32m+[0m[0m id       = (known after apply)
      [32m+[0m[0m location = "westus2"
      [32m+[0m[0m name     = "rg-devtest-assessments-dev-001"
      [32m+[0m[0m tags     = {
          [32m+[0m[0m "environment" = "devtest"
        }
    }

[1mPlan:[0m 1 to add, 0 to change, 1 to destroy.
[0m[90m
─────────────────────────────────────────────────────────────────────────────[0m

Note: You didn't use the -out option to save this plan, so Terraform can't
guarantee to take exactly these actions if you run "terraform apply" now.
Releasing state lock. This may take a few moments...
