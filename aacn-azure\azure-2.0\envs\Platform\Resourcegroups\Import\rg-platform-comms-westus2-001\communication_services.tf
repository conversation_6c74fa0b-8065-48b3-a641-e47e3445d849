# Azure Communication Service
resource "azurerm_communication_service" "acs" {
  name                = "acs-platform-comms-001"
  resource_group_name = var.resource_group_name
  data_location       = "United States"
  tags                = var.resource_group_tags
}

# Email Communication Services Domain
resource "azurerm_email_communication_service_domain" "email_domain" {
  name              = "aacn.org"
  email_service_id  = azurerm_email_communication_service.email_service.id
  domain_management = "CustomerManaged"
}

# Email Communication Service
resource "azurerm_email_communication_service" "email_service" {
  name                = "acssmtp-platform-comms-001"
  resource_group_name = var.resource_group_name
  data_location       = "United States"
  tags                = var.resource_group_tags
}