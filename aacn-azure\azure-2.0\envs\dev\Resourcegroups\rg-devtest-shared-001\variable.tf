variable "resource_group_name" {
  default = "rg-devtest-shared-001"
}

variable "location" {
  default = "West US 2"
}

variable "subscription_id" {
  default = "067e726d-1a79-4cc3-aba1-7fb1398638b5"
}

variable "tenant_id" {
  default = "ca4fd2d7-85ea-42d2-b7b3-30ef2666c7ab"
}

variable "resource_group_tags" {
  type = map(string)
  default = {
    environment = "devtest"
    # CostCenter = "1911-51210"
  }
}
variable "key_vault_names" {
  description = "List of Key Vault names to be created"
  type        = list(string)
  default     = [
    "kv-devtest-cert-001",
    "kv-devtest-member-001",
    "kv-devtest-NE-001",
    "kv-devtest-NTI-001",
    "kv-devtest-LE-001",
    "kv-devtest-chapters-001",
  ]
}


variable "sku" {
  default = "standard"
}

## Key vaults Variable
variable "key_vaults_tags" {
  type = map(string)
  default = {
    environment = "Platform"
    # CostCenter  = "1911-51210"
  }
}

# Object IDs for access policies
# variable "devops_leads_object_id" {
#   type        = string
#   description = "Object ID for the DevOps Leads group"
#   default = "5866b75a-2207-4212-ad24-3b725610b2a1"
# }

# variable "dynatech_engineers_object_id" {
#   type        = string
#   description = "Object ID for the DynaTech Engineers group"
#   default = "485da724-018a-4d86-8aee-adf46326de2f"
# }

# variable "aacn_dps_it_operations_object_id" {
#   type        = string
#   description = "Object ID for the AACN-DPS-IT Operations application"
#   default = "45f280a8-4460-4ac1-bcbd-ca26d65a679e"
# }

variable "access_policy_object_ids" {
  description = "List of object IDs for access policies"
  type        = list(string)
  default     = [
    "5866b75a-2207-4212-ad24-3b725610b2a1",
    "485da724-018a-4d86-8aee-adf46326de2f",
    "45f280a8-4460-4ac1-bcbd-ca26d65a679e",
    "0c6c6f5a-9f1c-4a56-8e4f-fe94df081d01",
  ]
}

variable "secret_permissions" {
  description = "Default secret permissions for Key Vault access policy"
  type        = list(string)
  default     = ["Get", "List" , "Delete", "Recover", "Backup", "Restore", "Purge", "Set"]
}

variable "key_permissions" {
  description = "Default key permissions for Key Vault access policy"
  type        = list(string)
  default     = ["Get", "List" , "Update", "Create", "Import", "Delete", "Recover", "Backup", "Restore", "Decrypt", "Encrypt", "Purge", "Sign", "UnwrapKey", "Verify", "WrapKey"]
}

variable "certificate_permissions" {
  description = "Default certificate permissions for Key Vault access policy"
  type        = list(string)
  default     = ["Get", "List" , "Update", "Create", "Import", "Delete", "Recover", "Backup", "Restore", "DeleteIssuers", "GetIssuers", "ListIssuers", "ManageContacts", "ManageIssuers", "SetIssuers"]
}

# App configuration variable 

variable "app_configuration_list" {
  description = "List of app configuration settings"
  type = list(object({
    name                = string
    sku                 = string
    enabled             = bool
    tags                = map(string)
  }))

  default = [
    {
      name    = "appcs-devtest-cert-001"
      sku     = "standard"
      enabled = true
      tags    = { environment = "prod" }
    },
    {
      name    = "appcs-devtest-member-001"
      sku     = "standard"
      enabled = true
      tags    = { environment = "prod" }
    },
    {
      name    = "appcs-devtest-NE-001"
      sku     = "standard"
      enabled = true
      tags    = { environment = "prod" }
    },
    {
      name    = "appcs-devtest-NTI-001"
      sku     = "standard"
      enabled = true
      tags    = { environment = "prod" }
    },
    {
      name    = "appcs-devtest-LE-001"
      sku     = "standard"
      enabled = true
      tags    = { environment = "prod" }
    },
    {
      name    = "appcs-devtest-chapters-001"
      sku     = "standard"
      enabled = true
      tags    = { environment = "prod" }
    }
  ]
}

# App Insight Variable 

variable "app_insights_list" {
  description = "List of Application Insights configurations"
  type = list(object({
    name             = string
    application_type = string
    workspace_id        = optional(string, null)
    tags             = map(string)
  }))

  default = [
    {
      name             = "appi-devtest-cert-001"
      application_type = "web"
      tags             = { environment = "dev" }
    },
    {
      name             = "appi-devtest-member-001"
      application_type = "web"
      tags             = { environment = "dev" }
    },
    {
      name             = "appi-devtest-NE-001"
      application_type = "web"
      tags             = { environment = "dev" }
    },
    {
      name             = "appi-devtest-NTI-001"
      application_type = "web"
      tags             = { environment = "dev" }
    },
    {
      name             = "appi-devtest-LE-001"
      application_type = "web"
      tags             = { environment = "dev" }
    },
    {
      name             = "appi-devtest-chapters-001"
      application_type = "web"
      tags             = { environment = "dev" }
    }
  ]
}

variable "log_analytics_workspace_name" {
  default = "work-devtest-shared-001"
}

variable "log_analytics_workspace_sku" {
  default = "PerGB2018"
}

variable "log_analytics_retention_in_days" {
  default = "30"
}

variable "log_analytics_tags" {
  type = map(string)
  default = {
    Environment = "dev"
    CostCenter = "1911-51210"
  }
}