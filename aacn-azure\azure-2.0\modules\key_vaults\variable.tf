variable "location" {
  description = "The location/region where the Key Vault will be created"
}

variable "key_vault_names" {
  description = "A map of Key Vault names to create."
  type        = map(string)
}

variable "resource_group_name" {
  description = "The name of the resource group in which to create the Key Vault"
}


variable "sku" {
  description = "The SKU name for the Key Vault"
}

variable "tenant_id" {
  description = "The tenant ID for the Azure subscription"
}

variable "key_vaults_tags" {
  description = "The tags of the key_vaults"
  type        = map(string)
}

variable "access_policies" {
  description = "List of access policies for Key Vault"
  type = list(object({
    object_id             = string
    tenant_id             = string
    secret_permissions    = list(string)
    key_permissions       = list(string)
    certificate_permissions = list(string)
    key_vault_name         = string
  }))
}

