resource "azurerm_resource_group" "res-0" {
  location = "westus2"
  name     = "rg-platform-connectivity-westus2-001"
  tags = {
    CostCenter  = "1911-51210"
    environment = "Platform"
  }
}
resource "azurerm_cdn_frontdoor_profile" "res-1" {
  name                = "afd-platform-hub-westus2-001"
  resource_group_name = "rg-platform-connectivity-westus2-001"
  sku_name            = "Standard_AzureFrontDoor"
  depends_on = [
    azurerm_resource_group.res-0
  ]
}
resource "azurerm_cdn_frontdoor_endpoint" "res-2" {
  cdn_frontdoor_profile_id = azurerm_cdn_frontdoor_profile.res-1.id
  name                     = "afdend-wwwaacnorg-dev"
}
resource "azurerm_cdn_frontdoor_route" "res-3" {
  cdn_frontdoor_endpoint_id     = azurerm_cdn_frontdoor_endpoint.res-2.id
  cdn_frontdoor_origin_group_id = azurerm_cdn_frontdoor_origin_group.res-4.id
  name                          = "default-route"
  patterns_to_match             = ["/*"]
  supported_protocols           = ["Http", "Https"]
}
resource "azurerm_cdn_frontdoor_origin_group" "res-4" {
  cdn_frontdoor_profile_id = azurerm_cdn_frontdoor_profile.res-1.id
  name                     = "afdpool-wwwaacnorg-dev"
  health_probe {
    interval_in_seconds = 100
    protocol            = "Https"
  }
  load_balancing {
  }
}
resource "azurerm_cdn_frontdoor_origin" "res-5" {
  cdn_frontdoor_origin_group_id  = azurerm_cdn_frontdoor_origin_group.res-4.id
  certificate_name_check_enabled = true
  host_name                      = "web-chaptersportal-dev-001.azurewebsites.net"
  name                           = "afdpool-wwwaacnorg-dev-origin"
  origin_host_header             = "web-chaptersportal-dev-001.azurewebsites.net"
  weight                         = 1000
}
resource "azurerm_bastion_host" "res-6" {
  location            = "westus2"
  name                = "bast-platform-hub-westus2-001"
  resource_group_name = "rg-platform-connectivity-westus2-001"
  sku                 = "Standard"
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
  zones = ["1", "2"]
  ip_configuration {
    name                 = "IpConf"
    public_ip_address_id = azurerm_public_ip.res-49.id
    subnet_id            = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/AzureBastionSubnet"
  }
  depends_on = [
    # One of azurerm_subnet.res-54,azurerm_subnet_network_security_group_association.res-55 (can't auto-resolve as their ids are identical)
  ]
}
resource "azurerm_virtual_network_gateway_connection" "res-7" {
  local_network_gateway_id   = azurerm_local_network_gateway.res-14.id
  location                   = "westus2"
  name                       = "cn-gw-l-platform-westus2-001-to-gw-v-production-westus2-001"
  resource_group_name        = "rg-platform-connectivity-westus2-001"
  type                       = "IPsec"
  virtual_network_gateway_id = azurerm_virtual_network_gateway.res-52.id
}
resource "azurerm_private_dns_resolver_dns_forwarding_ruleset" "res-8" {
  location                                   = "westus2"
  name                                       = "dnsprend-ruleset-aacnorg"
  private_dns_resolver_outbound_endpoint_ids = [azurerm_private_dns_resolver_outbound_endpoint.res-13.id]
  resource_group_name                        = "rg-platform-connectivity-westus2-001"
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}
resource "azurerm_private_dns_resolver_forwarding_rule" "res-9" {
  dns_forwarding_ruleset_id = azurerm_private_dns_resolver_dns_forwarding_ruleset.res-8.id
  domain_name               = "azure.aacn.org."
  name                      = "dnsprend-rule-aacnorg-to-azureaacnorg"
  target_dns_servers {
    ip_address = "********"
    port       = 53
  }
  target_dns_servers {
    ip_address = "********"
    port       = 53
  }
}
resource "azurerm_private_dns_resolver_virtual_network_link" "res-10" {
  dns_forwarding_ruleset_id = azurerm_private_dns_resolver_dns_forwarding_ruleset.res-8.id
  name                      = "vnet-platform-hub-westus2-001-link"
  virtual_network_id        = azurerm_virtual_network.res-53.id
}
resource "azurerm_private_dns_resolver" "res-11" {
  location            = "westus2"
  name                = "dnspr-platform-aacnorg-001"
  resource_group_name = "rg-platform-connectivity-westus2-001"
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
  virtual_network_id = azurerm_virtual_network.res-53.id
}
resource "azurerm_private_dns_resolver_inbound_endpoint" "res-12" {
  location                = "westus2"
  name                    = "dnsprend-aacnorg-inbound"
  private_dns_resolver_id = azurerm_private_dns_resolver.res-11.id
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
  ip_configurations {
    subnet_id = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/snet-dns-inbound"
  }
  depends_on = [
    # One of azurerm_subnet.res-59,azurerm_subnet_network_security_group_association.res-60 (can't auto-resolve as their ids are identical)
  ]
}
resource "azurerm_private_dns_resolver_outbound_endpoint" "res-13" {
  location                = "westus2"
  name                    = "dnsprend-aacnorg-outbound"
  private_dns_resolver_id = azurerm_private_dns_resolver.res-11.id
  subnet_id               = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/snet-dns-outbound"
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
  depends_on = [
    # One of azurerm_subnet.res-61,azurerm_subnet_network_security_group_association.res-62 (can't auto-resolve as their ids are identical)
  ]
}
resource "azurerm_local_network_gateway" "res-14" {
  address_space       = ["*************/24", "**********/22"]
  gateway_address     = "************"
  location            = "westus2"
  name                = "gw-l-platform-hub-vpn-hq-westus2-001"
  resource_group_name = "rg-platform-connectivity-westus2-001"
  depends_on = [
    azurerm_resource_group.res-0
  ]
}
resource "azurerm_network_security_group" "res-15" {
  location            = "westus2"
  name                = "nsg-snet-bastion"
  resource_group_name = "rg-platform-connectivity-westus2-001"
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
  depends_on = [
    azurerm_resource_group.res-0
  ]
}
resource "azurerm_network_security_rule" "res-16" {
  access                      = "Allow"
  destination_address_prefix  = "AzureCloud"
  destination_port_range      = "443"
  direction                   = "Outbound"
  name                        = "AllowAzureCloudOutbound"
  network_security_group_name = "nsg-snet-bastion"
  priority                    = 105
  protocol                    = "Tcp"
  resource_group_name         = "rg-platform-connectivity-westus2-001"
  source_address_prefix       = "*"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.res-15
  ]
}
resource "azurerm_network_security_rule" "res-17" {
  access                      = "Allow"
  destination_address_prefix  = "*"
  destination_port_range      = "443"
  direction                   = "Inbound"
  name                        = "AllowHealthProbe"
  network_security_group_name = "nsg-snet-bastion"
  priority                    = 130
  protocol                    = "Tcp"
  resource_group_name         = "rg-platform-connectivity-westus2-001"
  source_address_prefix       = "AzureLoadBalancer"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.res-15
  ]
}
resource "azurerm_network_security_rule" "res-18" {
  access                      = "Allow"
  destination_address_prefix  = "Internet"
  destination_port_range      = "80"
  direction                   = "Outbound"
  name                        = "AllowHttpOutbound"
  network_security_group_name = "nsg-snet-bastion"
  priority                    = 130
  protocol                    = "Tcp"
  resource_group_name         = "rg-platform-connectivity-westus2-001"
  source_address_prefix       = "*"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.res-15
  ]
}
resource "azurerm_network_security_rule" "res-19" {
  access                      = "Allow"
  destination_address_prefix  = "*"
  destination_port_range      = "443"
  direction                   = "Inbound"
  name                        = "AllowHttpsGatewayManager"
  network_security_group_name = "nsg-snet-bastion"
  priority                    = 110
  protocol                    = "Tcp"
  resource_group_name         = "rg-platform-connectivity-westus2-001"
  source_address_prefix       = "GatewayManager"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.res-15
  ]
}
resource "azurerm_network_security_rule" "res-20" {
  access                      = "Allow"
  destination_address_prefix  = "*"
  destination_port_range      = "443"
  direction                   = "Inbound"
  name                        = "AllowHttpsInternet"
  network_security_group_name = "nsg-snet-bastion"
  priority                    = 100
  protocol                    = "Tcp"
  resource_group_name         = "rg-platform-connectivity-westus2-001"
  source_address_prefix       = "Internet"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.res-15
  ]
}
resource "azurerm_network_security_rule" "res-21" {
  access                      = "Allow"
  destination_address_prefix  = "*"
  destination_port_ranges     = ["5701", "8080"]
  direction                   = "Inbound"
  name                        = "AllowInternalBastion"
  network_security_group_name = "nsg-snet-bastion"
  priority                    = 120
  protocol                    = "Tcp"
  resource_group_name         = "rg-platform-connectivity-westus2-001"
  source_address_prefix       = "VirtualNetwork"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.res-15
  ]
}
resource "azurerm_network_security_rule" "res-22" {
  access                      = "Allow"
  destination_address_prefix  = "VirtualNetwork"
  destination_port_ranges     = ["5701", "8080"]
  direction                   = "Outbound"
  name                        = "AllowInternalBastionOutbound"
  network_security_group_name = "nsg-snet-bastion"
  priority                    = 110
  protocol                    = "Tcp"
  resource_group_name         = "rg-platform-connectivity-westus2-001"
  source_address_prefix       = "VirtualNetwork"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.res-15
  ]
}
resource "azurerm_network_security_rule" "res-23" {
  access                      = "Allow"
  destination_address_prefix  = "VirtualNetwork"
  destination_port_ranges     = ["22", "3389"]
  direction                   = "Outbound"
  name                        = "AllowRdpSsh"
  network_security_group_name = "nsg-snet-bastion"
  priority                    = 100
  protocol                    = "Tcp"
  resource_group_name         = "rg-platform-connectivity-westus2-001"
  source_address_prefix       = "*"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.res-15
  ]
}
resource "azurerm_network_security_group" "res-24" {
  location            = "westus2"
  name                = "nsg-snet-cloudpcs-test"
  resource_group_name = "rg-platform-connectivity-westus2-001"
  depends_on = [
    azurerm_resource_group.res-0
  ]
}
resource "azurerm_network_security_rule" "res-25" {
  access                      = "Allow"
  destination_address_prefix  = "*"
  destination_port_range      = "5986"
  direction                   = "Inbound"
  name                        = "AllowPSRemoting"
  network_security_group_name = "nsg-snet-cloudpcs-test"
  priority                    = 110
  protocol                    = "Tcp"
  resource_group_name         = "rg-platform-connectivity-westus2-001"
  source_address_prefix       = "*"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.res-24
  ]
}
resource "azurerm_network_security_group" "res-26" {
  location            = "westus2"
  name                = "nsg-snet-dns-inbound"
  resource_group_name = "rg-platform-connectivity-westus2-001"
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
  depends_on = [
    azurerm_resource_group.res-0
  ]
}
resource "azurerm_network_security_rule" "res-27" {
  access                      = "Allow"
  destination_address_prefix  = "*"
  destination_port_range      = "53"
  direction                   = "Inbound"
  name                        = "AllowAzureServicesTcp"
  network_security_group_name = "nsg-snet-dns-inbound"
  priority                    = 200
  protocol                    = "Tcp"
  resource_group_name         = "rg-platform-connectivity-westus2-001"
  source_address_prefix       = "AzureCloud"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.res-26
  ]
}
resource "azurerm_network_security_rule" "res-28" {
  access                      = "Allow"
  destination_address_prefix  = "*"
  destination_port_range      = "53"
  direction                   = "Inbound"
  name                        = "AllowAzureServicesUdp"
  network_security_group_name = "nsg-snet-dns-inbound"
  priority                    = 201
  protocol                    = "Udp"
  resource_group_name         = "rg-platform-connectivity-westus2-001"
  source_address_prefix       = "AzureCloud"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.res-26
  ]
}
resource "azurerm_network_security_rule" "res-29" {
  access                      = "Allow"
  destination_address_prefix  = "*"
  destination_port_range      = "53"
  direction                   = "Inbound"
  name                        = "AllowDNSInboundTcp"
  network_security_group_name = "nsg-snet-dns-inbound"
  priority                    = 100
  protocol                    = "Tcp"
  resource_group_name         = "rg-platform-connectivity-westus2-001"
  source_address_prefix       = "VirtualNetwork"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.res-26
  ]
}
resource "azurerm_network_security_rule" "res-30" {
  access                      = "Allow"
  destination_address_prefix  = "*"
  destination_port_range      = "53"
  direction                   = "Inbound"
  name                        = "AllowDNSInboundUdp"
  network_security_group_name = "nsg-snet-dns-inbound"
  priority                    = 101
  protocol                    = "Udp"
  resource_group_name         = "rg-platform-connectivity-westus2-001"
  source_address_prefix       = "VirtualNetwork"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.res-26
  ]
}
resource "azurerm_network_security_group" "res-31" {
  location            = "westus2"
  name                = "nsg-snet-dns-outbound"
  resource_group_name = "rg-platform-connectivity-westus2-001"
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
  depends_on = [
    azurerm_resource_group.res-0
  ]
}
resource "azurerm_network_security_rule" "res-32" {
  access                      = "Allow"
  destination_address_prefix  = "*"
  destination_port_range      = "53"
  direction                   = "Outbound"
  name                        = "AllowDNSAzureServicesTcp"
  network_security_group_name = "nsg-snet-dns-outbound"
  priority                    = 201
  protocol                    = "Tcp"
  resource_group_name         = "rg-platform-connectivity-westus2-001"
  source_address_prefix       = "AzureCloud"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.res-31
  ]
}
resource "azurerm_network_security_rule" "res-33" {
  access                      = "Allow"
  destination_address_prefix  = "*"
  destination_port_range      = "53"
  direction                   = "Outbound"
  name                        = "AllowDNSAzureServicesUdp"
  network_security_group_name = "nsg-snet-dns-outbound"
  priority                    = 202
  protocol                    = "Udp"
  resource_group_name         = "rg-platform-connectivity-westus2-001"
  source_address_prefix       = "AzureCloud"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.res-31
  ]
}
resource "azurerm_network_security_group" "res-34" {
  location            = "westus2"
  name                = "nsg-snet-identity-dc"
  resource_group_name = "rg-platform-connectivity-westus2-001"
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
  depends_on = [
    azurerm_resource_group.res-0
  ]
}
resource "azurerm_network_security_rule" "res-35" {
  access                      = "Allow"
  destination_address_prefix  = "*"
  destination_port_range      = "5986"
  direction                   = "Inbound"
  name                        = "AllowPSRemoting"
  network_security_group_name = "nsg-snet-identity-dc"
  priority                    = 301
  protocol                    = "Tcp"
  resource_group_name         = "rg-platform-connectivity-westus2-001"
  source_address_prefix       = "AzureActiveDirectoryDomainServices"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.res-34
  ]
}
resource "azurerm_network_security_group" "res-36" {
  location            = "westus2"
  name                = "nsg-snet-privatelink-storage"
  resource_group_name = "rg-platform-connectivity-westus2-001"
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
  depends_on = [
    azurerm_resource_group.res-0
  ]
}
resource "azurerm_network_security_rule" "res-37" {
  access                      = "Allow"
  destination_address_prefix  = "Storage"
  destination_port_range      = "443"
  direction                   = "Outbound"
  name                        = "AllowStorageOutbound"
  network_security_group_name = "nsg-snet-privatelink-storage"
  priority                    = 100
  protocol                    = "Tcp"
  resource_group_name         = "rg-platform-connectivity-westus2-001"
  source_address_prefix       = "VirtualNetwork"
  source_port_range           = "*"
  depends_on = [
    azurerm_network_security_group.res-36
  ]
}
resource "azurerm_private_dns_zone" "res-38" {
  name                = "aacn.org"
  resource_group_name = "rg-platform-connectivity-westus2-001"
  tags = {
    CostCenter  = "1911-51210"
    environment = "Platform"
  }
  depends_on = [
    azurerm_resource_group.res-0
  ]
}
resource "azurerm_private_dns_zone" "res-40" {
  name                = "azure.aacn.org"
  resource_group_name = "rg-platform-connectivity-westus2-001"
  tags = {
    CostCenter  = "1911-51210"
    environment = "prod"
  }
  depends_on = [
    azurerm_resource_group.res-0
  ]
}
resource "azurerm_private_dns_a_record" "res-41" {
  name                = "pz6-w7iokkbawg6"
  records             = ["********"]
  resource_group_name = "rg-platform-connectivity-westus2-001"
  ttl                 = 10
  zone_name           = "azure.aacn.org"
  depends_on = [
    azurerm_private_dns_zone.res-40
  ]
}
resource "azurerm_private_dns_a_record" "res-42" {
  name                = "tg1hgibhaox0oak"
  records             = ["********"]
  resource_group_name = "rg-platform-connectivity-westus2-001"
  ttl                 = 10
  zone_name           = "azure.aacn.org"
  depends_on = [
    azurerm_private_dns_zone.res-40
  ]
}
resource "azurerm_private_dns_a_record" "res-43" {
  name                = "vm000001"
  records             = ["10.2.2.69"]
  resource_group_name = "rg-platform-connectivity-westus2-001"
  ttl                 = 10
  zone_name           = "azure.aacn.org"
  depends_on = [
    azurerm_private_dns_zone.res-40
  ]
}
resource "azurerm_private_dns_a_record" "res-44" {
  name                = "vm000002"
  records             = ["10.2.2.70"]
  resource_group_name = "rg-platform-connectivity-westus2-001"
  ttl                 = 10
  zone_name           = "azure.aacn.org"
  depends_on = [
    azurerm_private_dns_zone.res-40
  ]
}
resource "azurerm_private_dns_a_record" "res-45" {
  name                = "vpngw000000"
  records             = ["10.2.1.4"]
  resource_group_name = "rg-platform-connectivity-westus2-001"
  ttl                 = 10
  zone_name           = "azure.aacn.org"
  depends_on = [
    azurerm_private_dns_zone.res-40
  ]
}
resource "azurerm_private_dns_a_record" "res-46" {
  name                = "vpngw000001"
  records             = ["10.2.1.5"]
  resource_group_name = "rg-platform-connectivity-westus2-001"
  ttl                 = 10
  zone_name           = "azure.aacn.org"
  depends_on = [
    azurerm_private_dns_zone.res-40
  ]
}
resource "azurerm_private_dns_zone_virtual_network_link" "res-48" {
  name                  = "link-azureaacnorg-to-platform-hub"
  private_dns_zone_name = "azure.aacn.org"
  registration_enabled  = true
  resource_group_name   = "rg-platform-connectivity-westus2-001"
  virtual_network_id    = azurerm_virtual_network.res-53.id
  depends_on = [
    azurerm_private_dns_zone.res-40
  ]
}
resource "azurerm_public_ip" "res-49" {
  allocation_method   = "Static"
  location            = "westus2"
  name                = "pip-bast-platform-hub-westus2-001"
  resource_group_name = "rg-platform-connectivity-westus2-001"
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
  zones = ["1", "2", "3"]
  depends_on = [
    azurerm_resource_group.res-0
  ]
}
resource "azurerm_public_ip" "res-50" {
  allocation_method   = "Static"
  location            = "westus2"
  name                = "pip-gw-vnet-platform-hub-westus2-001"
  resource_group_name = "rg-platform-connectivity-westus2-001"
  zones               = ["1", "2", "3"]
  depends_on = [
    azurerm_resource_group.res-0
  ]
}
resource "azurerm_route_table" "res-51" {
  location            = "westus2"
  name                = "rt-testdb-poc"
  resource_group_name = "rg-platform-connectivity-westus2-001"
  depends_on = [
    azurerm_resource_group.res-0
  ]
}
resource "azurerm_virtual_network_gateway" "res-52" {
  location            = "westus2"
  name                = "gw-v-vnet-platform-hub-westus2-001"
  resource_group_name = "rg-platform-connectivity-westus2-001"
  sku                 = "VpnGw2AZ"
  type                = "Vpn"
  ip_configuration {
    name                 = "vnet-gateway-config"
    public_ip_address_id = azurerm_public_ip.res-50.id
    subnet_id            = azurerm_subnet.res-56.id
  }
}
resource "azurerm_virtual_network" "res-53" {
  address_space       = ["********/16"]
  location            = "westus2"
  name                = "vnet-platform-hub-westus2-001"
  resource_group_name = "rg-platform-connectivity-westus2-001"
  depends_on = [
    azurerm_resource_group.res-0
  ]
}
resource "azurerm_subnet" "res-54" {
  address_prefixes                = ["*********/26"]
  default_outbound_access_enabled = false
  name                            = "AzureBastionSubnet"
  resource_group_name             = "rg-platform-connectivity-westus2-001"
  virtual_network_name            = "vnet-platform-hub-westus2-001"
  depends_on = [
    azurerm_virtual_network.res-53
  ]
}
resource "azurerm_subnet_network_security_group_association" "res-55" {
  network_security_group_id = azurerm_network_security_group.res-15.id
  subnet_id                 = azurerm_subnet.res-54.id
}
resource "azurerm_subnet" "res-56" {
  address_prefixes                  = ["********/24"]
  name                              = "GatewaySubnet"
  private_endpoint_network_policies = "Enabled"
  resource_group_name               = "rg-platform-connectivity-westus2-001"
  virtual_network_name              = "vnet-platform-hub-westus2-001"
  depends_on = [
    azurerm_virtual_network.res-53
  ]
}
resource "azurerm_subnet" "res-57" {
  address_prefixes     = ["*********/29"]
  name                 = "snet-cloudpcs-test"
  resource_group_name  = "rg-platform-connectivity-westus2-001"
  virtual_network_name = "vnet-platform-hub-westus2-001"
  depends_on = [
    azurerm_virtual_network.res-53
  ]
}
resource "azurerm_subnet_network_security_group_association" "res-58" {
  network_security_group_id = azurerm_network_security_group.res-24.id
  subnet_id                 = azurerm_subnet.res-57.id
}
resource "azurerm_subnet" "res-59" {
  address_prefixes                = ["********/28"]
  default_outbound_access_enabled = false
  name                            = "snet-dns-inbound"
  resource_group_name             = "rg-platform-connectivity-westus2-001"
  virtual_network_name            = "vnet-platform-hub-westus2-001"
  delegation {
    name = "Microsoft.Network/dnsResolvers"
    service_delegation {
      actions = ["Microsoft.Network/virtualNetworks/subnets/join/action"]
      name    = "Microsoft.Network/dnsResolvers"
    }
  }
  depends_on = [
    azurerm_virtual_network.res-53
  ]
}
resource "azurerm_subnet_network_security_group_association" "res-60" {
  network_security_group_id = azurerm_network_security_group.res-26.id
  subnet_id                 = azurerm_subnet.res-59.id
}
resource "azurerm_subnet" "res-61" {
  address_prefixes                = ["*********/28"]
  default_outbound_access_enabled = false
  name                            = "snet-dns-outbound"
  resource_group_name             = "rg-platform-connectivity-westus2-001"
  virtual_network_name            = "vnet-platform-hub-westus2-001"
  delegation {
    name = "Microsoft.Network/dnsResolvers"
    service_delegation {
      actions = ["Microsoft.Network/virtualNetworks/subnets/join/action"]
      name    = "Microsoft.Network/dnsResolvers"
    }
  }
  depends_on = [
    azurerm_virtual_network.res-53
  ]
}
resource "azurerm_subnet_network_security_group_association" "res-62" {
  network_security_group_id = azurerm_network_security_group.res-31.id
  subnet_id                 = azurerm_subnet.res-61.id
}
resource "azurerm_subnet" "res-63" {
  address_prefixes                  = ["********/24"]
  name                              = "snet-identity-dc"
  private_endpoint_network_policies = "Enabled"
  resource_group_name               = "rg-platform-connectivity-westus2-001"
  virtual_network_name              = "vnet-platform-hub-westus2-001"
  depends_on = [
    azurerm_virtual_network.res-53
  ]
}
resource "azurerm_subnet_network_security_group_association" "res-64" {
  network_security_group_id = azurerm_network_security_group.res-34.id
  subnet_id                 = azurerm_subnet.res-63.id
}
resource "azurerm_subnet" "res-65" {
  address_prefixes                = ["**********/27"]
  default_outbound_access_enabled = false
  name                            = "snet-privatelink-storage"
  resource_group_name             = "rg-platform-connectivity-westus2-001"
  virtual_network_name            = "vnet-platform-hub-westus2-001"
  delegation {
    name = "dlg-testdb-poc"
    service_delegation {
      actions = ["Microsoft.Network/virtualNetworks/subnets/join/action", "Microsoft.Network/virtualNetworks/subnets/prepareNetworkPolicies/action", "Microsoft.Network/virtualNetworks/subnets/unprepareNetworkPolicies/action"]
      name    = "Microsoft.Sql/managedInstances"
    }
  }
  depends_on = [
    azurerm_virtual_network.res-53
  ]
}
resource "azurerm_subnet_network_security_group_association" "res-66" {
  network_security_group_id = azurerm_network_security_group.res-36.id
  subnet_id                 = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/snet-privatelink-storage"
  depends_on = [
    azurerm_subnet.res-65
    # One of azurerm_subnet.res-65,azurerm_subnet_route_table_association.res-67 (can't auto-resolve as their ids are identical)
  ]
}
resource "azurerm_subnet_route_table_association" "res-67" {
  route_table_id = azurerm_route_table.res-51.id
  subnet_id      = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/snet-privatelink-storage"
  depends_on = [
    azurerm_subnet.res-65
    # One of azurerm_subnet.res-65,azurerm_subnet_network_security_group_association.res-66 (can't auto-resolve as their ids are identical)
  ]
}
resource "azurerm_virtual_network_peering" "res-68" {
  allow_forwarded_traffic   = true
  name                      = "hub-to-dev"
  remote_virtual_network_id = "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-connectivity-001/providers/Microsoft.Network/virtualNetworks/vnet-westus2-devtest-dev"
  resource_group_name       = "rg-platform-connectivity-westus2-001"
  virtual_network_name      = "vnet-platform-hub-westus2-001"
  depends_on = [
    azurerm_virtual_network.res-53
  ]
}
resource "azurerm_virtual_network_peering" "res-69" {
  allow_forwarded_traffic   = true
  name                      = "hub-to-prod"
  remote_virtual_network_id = "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-connectivity-prod-001/providers/Microsoft.Network/virtualNetworks/vnet-westus2-corp-prod"
  resource_group_name       = "rg-platform-connectivity-westus2-001"
  virtual_network_name      = "vnet-platform-hub-westus2-001"
  depends_on = [
    azurerm_virtual_network.res-53
  ]
}
resource "azurerm_virtual_network_peering" "res-70" {
  allow_forwarded_traffic   = true
  name                      = "hub_to_iland_spoke"
  remote_virtual_network_id = "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-connectivity-prod-001/providers/Microsoft.Network/virtualNetworks/vnet-production-iland-westus2-001"
  resource_group_name       = "rg-platform-connectivity-westus2-001"
  virtual_network_name      = "vnet-platform-hub-westus2-001"
  depends_on = [
    azurerm_virtual_network.res-53
  ]
}
