# module "key_vaults" {
#   source              = "../../../../../modules/key_vaults"  
#   resource_group_name = var.resource_group_name     
#   location            = var.location
#   key_vaults_tags     = var.key_vaults_tags
#   tenant_id           = var.tenant_id
#   sku                 = var.sku                 
  
#   key_vault_names = {
#     "kv-platform-terra-001"       = "kv-platform-terra-001"
#   }

#   access_policies = [
#     for object_id in var.access_policy_object_ids : {
#       key_vault_name          = "kv-platform-terra-001"
#       tenant_id               = var.tenant_id
#       object_id               = object_id
#       secret_permissions      = var.secret_permissions
#       key_permissions         = var.key_permissions
#       certificate_permissions = var.certificate_permissions
#     }
#   ]
# }