# resource "azurerm_frontdoor" "this" {
#   name                = var.frontdoor_name
#   resource_group_name = var.resource_group_name

#   routing_rule {
#     name               = "defaultRoutingRule"
#     accepted_protocols = var.accepted_protocols
#     patterns_to_match  = var.patterns_to_match
#     frontend_endpoints = [var.frontend_endpoint_name]
    
#     forwarding_configuration {
#       forwarding_protocol = "MatchRequest"
#       backend_pool_name   = var.backend_pool_name
#     }
#   }

#   backend_pool_load_balancing {
#     name = var.load_balancing_name
#   }

#   backend_pool_health_probe {
#     name = var.health_probe_name
#   }

#   backend_pool {
#     name = var.backend_pool_name
#     backend {
#       host_header = var.backend_host
#       address     = var.backend_address
#       http_port   = var.backend_http_port
#       https_port  = var.backend_https_port
#     }

#     load_balancing_name = var.load_balancing_name
#     health_probe_name   = var.health_probe_name
#   }

#   frontend_endpoint {
#     name      = var.frontend_endpoint_name
#     host_name = var.frontend_host_name
#   }
# }

#####
resource "azurerm_cdn_frontdoor_profile" "this" {
  name                = var.frontdoor_name
  resource_group_name = var.resource_group_name
  sku_name            = "Standard_AzureFrontDoor"
}

resource "azurerm_cdn_frontdoor_endpoint" "this" {
  name                     = var.frontend_endpoint_name
  cdn_frontdoor_profile_id = azurerm_cdn_frontdoor_profile.this.id
  enabled                  = true
}

resource "azurerm_cdn_frontdoor_origin_group" "this" {
  name                     = var.backend_pool_name
  cdn_frontdoor_profile_id = azurerm_cdn_frontdoor_profile.this.id
  
  load_balancing {
    sample_size                 = 4
    successful_samples_required = 3
  }

  health_probe {
    path                = "/"
    protocol            = "Https"
    interval_in_seconds = 100
  }
}

resource "azurerm_cdn_frontdoor_origin" "this" {
  name                          = "${var.backend_pool_name}-origin"
  cdn_frontdoor_origin_group_id = azurerm_cdn_frontdoor_origin_group.this.id
  
  enabled                       = true
  host_name                     = var.backend_address
  http_port                     = var.backend_http_port
  https_port                    = var.backend_https_port
  origin_host_header            = var.backend_host
  priority                      = 1
  weight                        = 1000
  certificate_name_check_enabled = true
}

resource "azurerm_cdn_frontdoor_route" "this" {
  name                          = "default-route"
  cdn_frontdoor_endpoint_id     = azurerm_cdn_frontdoor_endpoint.this.id
  cdn_frontdoor_origin_group_id = azurerm_cdn_frontdoor_origin_group.this.id
  cdn_frontdoor_origin_ids      = [azurerm_cdn_frontdoor_origin.this.id]
  enabled                       = true
  forwarding_protocol           = "MatchRequest"
  patterns_to_match             = var.patterns_to_match
  supported_protocols           = var.accepted_protocols
}
