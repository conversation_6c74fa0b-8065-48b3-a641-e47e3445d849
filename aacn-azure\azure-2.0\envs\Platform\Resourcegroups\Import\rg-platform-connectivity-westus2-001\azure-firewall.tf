# module "firewall" {
#   source              = "../../../../../modules/azure_firewall"
#   firewall_name       = var.firewall_name
#   location            = var.location
#   resource_group_name = var.resource_group_name
#   sku_name            = var.sku
#   sku_tier            = var.sku_tier
#   subnet_id           = azurerm_subnet.firewall_subnet.id
#   public_ip_id        = azurerm_public_ip.firewall_pip.id
#   tags = {
#     Environment = "Platform"
#   }
# }

# resource "azurerm_subnet" "firewall_subnet" {
#   name                 = var.firewall_subnet_name
#   resource_group_name  = var.resource_group_name
#   virtual_network_name = "vnet-platform-hub-westus2-001"
#   address_prefixes     = ["********/24"]
# }

# resource "azurerm_public_ip" "firewall_pip" {
#   name                = "firewall-pip"
#   resource_group_name = var.resource_group_name
#   location            = var.location
#   allocation_method   = "Static"
#   sku                 = "Standard"
# }