{"version": 4, "terraform_version": "1.7.5", "serial": 54, "lineage": "af9e6b67-8644-8f7b-a0ae-f8f122b93627", "outputs": {}, "resources": [{"module": "module.rg-devetst-mgmt-westus2-001.module.dev_storage", "mode": "managed", "type": "azurerm_storage_account", "name": "storage", "provider": "module.rg-devetst-mgmt-westus2-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 4, "attributes": {"access_tier": "Hot", "account_kind": "StorageV2", "account_replication_type": "LRS", "account_tier": "Standard", "allow_nested_items_to_be_public": true, "allowed_copy_scope": "", "azure_files_authentication": [], "blob_properties": [{"change_feed_enabled": false, "change_feed_retention_in_days": 0, "container_delete_retention_policy": [], "cors_rule": [], "default_service_version": "", "delete_retention_policy": [], "last_access_time_enabled": false, "restore_policy": [], "versioning_enabled": false}], "cross_tenant_replication_enabled": true, "custom_domain": [], "customer_managed_key": [], "default_to_oauth_authentication": false, "dns_endpoint_type": "Standard", "edge_zone": "", "enable_https_traffic_only": true, "https_traffic_only_enabled": true, "id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-mgmt-westus2-001/providers/Microsoft.Storage/storageAccounts/stdevtestterra001", "identity": [], "immutability_policy": [], "infrastructure_encryption_enabled": false, "is_hns_enabled": false, "large_file_share_enabled": false, "local_user_enabled": true, "location": "westus2", "min_tls_version": "TLS1_2", "name": "stdevtestterra001", "network_rules": [], "nfsv3_enabled": false, "primary_access_key": "****************************************************************************************", "primary_blob_connection_string": "DefaultEndpointsProtocol=https;BlobEndpoint=https://stdevtestterra001.blob.core.windows.net/;AccountName=stdevtestterra001;AccountKey=****************************************************************************************", "primary_blob_endpoint": "https://stdevtestterra001.blob.core.windows.net/", "primary_blob_host": "stdevtestterra001.blob.core.windows.net", "primary_blob_internet_endpoint": "", "primary_blob_internet_host": "", "primary_blob_microsoft_endpoint": "", "primary_blob_microsoft_host": "", "primary_connection_string": "DefaultEndpointsProtocol=https;AccountName=stdevtestterra001;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "primary_dfs_endpoint": "https://stdevtestterra001.dfs.core.windows.net/", "primary_dfs_host": "stdevtestterra001.dfs.core.windows.net", "primary_dfs_internet_endpoint": "", "primary_dfs_internet_host": "", "primary_dfs_microsoft_endpoint": "", "primary_dfs_microsoft_host": "", "primary_file_endpoint": "https://stdevtestterra001.file.core.windows.net/", "primary_file_host": "stdevtestterra001.file.core.windows.net", "primary_file_internet_endpoint": "", "primary_file_internet_host": "", "primary_file_microsoft_endpoint": "", "primary_file_microsoft_host": "", "primary_location": "westus2", "primary_queue_endpoint": "https://stdevtestterra001.queue.core.windows.net/", "primary_queue_host": "stdevtestterra001.queue.core.windows.net", "primary_queue_microsoft_endpoint": "", "primary_queue_microsoft_host": "", "primary_table_endpoint": "https://stdevtestterra001.table.core.windows.net/", "primary_table_host": "stdevtestterra001.table.core.windows.net", "primary_table_microsoft_endpoint": "", "primary_table_microsoft_host": "", "primary_web_endpoint": "https://stdevtestterra001.z5.web.core.windows.net/", "primary_web_host": "stdevtestterra001.z5.web.core.windows.net", "primary_web_internet_endpoint": "", "primary_web_internet_host": "", "primary_web_microsoft_endpoint": "", "primary_web_microsoft_host": "", "public_network_access_enabled": true, "queue_encryption_key_type": "Service", "queue_properties": [{"cors_rule": [], "hour_metrics": [{"enabled": true, "include_apis": true, "retention_policy_days": 7, "version": "1.0"}], "logging": [{"delete": false, "read": false, "retention_policy_days": 0, "version": "1.0", "write": false}], "minute_metrics": [{"enabled": false, "include_apis": false, "retention_policy_days": 0, "version": "1.0"}]}], "resource_group_name": "rg-devtest-mgmt-westus2-001", "routing": [], "sas_policy": [], "secondary_access_key": "****************************************************************************************", "secondary_blob_connection_string": "", "secondary_blob_endpoint": "", "secondary_blob_host": "", "secondary_blob_internet_endpoint": "", "secondary_blob_internet_host": "", "secondary_blob_microsoft_endpoint": "", "secondary_blob_microsoft_host": "", "secondary_connection_string": "DefaultEndpointsProtocol=https;AccountName=stdevtestterra001;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "secondary_dfs_endpoint": "", "secondary_dfs_host": "", "secondary_dfs_internet_endpoint": "", "secondary_dfs_internet_host": "", "secondary_dfs_microsoft_endpoint": "", "secondary_dfs_microsoft_host": "", "secondary_file_endpoint": "", "secondary_file_host": "", "secondary_file_internet_endpoint": "", "secondary_file_internet_host": "", "secondary_file_microsoft_endpoint": "", "secondary_file_microsoft_host": "", "secondary_location": "", "secondary_queue_endpoint": "", "secondary_queue_host": "", "secondary_queue_microsoft_endpoint": "", "secondary_queue_microsoft_host": "", "secondary_table_endpoint": "", "secondary_table_host": "", "secondary_table_microsoft_endpoint": "", "secondary_table_microsoft_host": "", "secondary_web_endpoint": "", "secondary_web_host": "", "secondary_web_internet_endpoint": "", "secondary_web_internet_host": "", "secondary_web_microsoft_endpoint": "", "secondary_web_microsoft_host": "", "sftp_enabled": false, "share_properties": [{"cors_rule": [], "retention_policy": [{"days": 7}], "smb": []}], "shared_access_key_enabled": true, "static_website": [], "table_encryption_key_type": "Service", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devetst-mgmt-westus2-001.module.dev_storage", "mode": "managed", "type": "azurerm_storage_container", "name": "tfstate", "provider": "module.rg-devetst-mgmt-westus2-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 1, "attributes": {"container_access_type": "private", "default_encryption_scope": "$account-encryption-key", "encryption_scope_override_enabled": true, "has_immutability_policy": false, "has_legal_hold": false, "id": "https://stdevtestterra001.blob.core.windows.net/tfstate", "metadata": {}, "name": "tfstate", "resource_manager_id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-mgmt-westus2-001/providers/Microsoft.Storage/storageAccounts/stdevtestterra001/blobServices/default/containers/tfstate", "storage_account_name": "stdevtestterra001", "timeouts": null}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["module.rg-devetst-mgmt-westus2-001.module.dev_storage.azurerm_storage_account.storage"]}]}, {"module": "module.rg-devetst-mgmt-westus2-001.module.rg-devetst-mgmt-westus2-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devetst-mgmt-westus2-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-mgmt-westus2-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-mgmt-westus2-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devtest-IIS-dev-001.module.rg-devtest-IIS-dev-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devtest-IIS-dev-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-IIS-dev-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-IIS-dev-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devtest-aacnorg-dev-001.module.rg-devtest-aacnorg-dev-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devtest-aacnorg-dev-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-aacnorg-dev-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-aacnorg-dev-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devtest-assessment-dev-001.module.rg-devtest-assessment-dev-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devtest-assessment-dev-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-assessment-dev-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-assessment-dev-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devtest-cert-cequeue-001.module.rg-devtest-cert-cequeue-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devtest-cert-cequeue-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-cert-cequeue-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-cert-cequeue-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devtest-cert-certtestcenter-001.module.rg-devtest-cert-certtestcenter-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devtest-cert-certtestcenter-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-cert-certtestcenter-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-cert-certtestcenter-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devtest-chapters-nursingnerwork-001.module.rg-devtest-chapters-nursingnerwork-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devtest-chapters-nursingnerwork-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-chapters-nursingnerwork-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-chapters-nursingnerwork-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devtest-connectivity-001.module.rg-devtest-connectivity-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devtest-connectivity-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-connectivity-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-connectivity-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devtest-hwe-hweatteams-001.module.rg-devtest-hwe-hweatteams-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devtest-hwe-hweatteams-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-hwe-hweatteams-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-hwe-hweatteams-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devtest-learning-courses-001.module.rg-devtest-learning-courses-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devtest-learning-courses-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-learning-courses-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-learning-courses-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devtest-lighthouse-DW-001.module.rg-devtest-lighthouse-DW-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devtest-lighthouse-DW-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-lighthouse-DW-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-lighthouse-DW-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devtest-lighthouse-fabric-001.module.rg-devtest-lighthouse-fabric-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devtest-lighthouse-fabric-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-lighthouse-fabric-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-lighthouse-fabric-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devtest-lighthouse-taxonomy-001.module.rg-devtest-lighthouse-taxonomy-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devtest-lighthouse-taxonomy-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-lighthouse-taxonomy-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-lighthouse-taxonomy-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devtest-netforum-dev-001.module.rg-devtest-netforum-dev-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devtest-netforum-dev-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-netforum-dev-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-netforum-dev-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devtest-nti-eventsv3-001.module.rg-devtest-nti-eventsv3-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devtest-nti-eventsv3-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-nti-eventsv3-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-nti-eventsv3-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devtest-nti-programapproval-001.module.rg-devtest-nti-programapproval-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devtest-nti-programapproval-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-nti-programapproval-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-nti-programapproval-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devtest-nursing-beaconsurvey-001.module.rg-devtest-nursing-beaconsurvey-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devtest-nursing-beaconsurvey-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-nursing-beaconsurvey-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-nursing-beaconsurvey-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devtest-nursing-beaconv3-001.module.rg-devtest-nursing-beaconv3-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devtest-nursing-beaconv3-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-nursing-beaconv3-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-nursing-beaconv3-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devtest-online-cert-shared-001.module.rg-devtest-online-cert-shared-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devtest-online-cert-shared-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-online-cert-shared-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-online-cert-shared-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-devtest-taxonomy-dev-001.module.rg-devtest-taxonomy-dev-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-devtest-taxonomy-dev-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/067e726d-1a79-4cc3-aba1-7fb1398638b5/resourceGroups/rg-devtest-taxonomy-dev-001", "location": "westus2", "managed_by": "", "name": "rg-devtest-taxonomy-dev-001", "tags": {"environment": "devtest"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}], "check_results": null}