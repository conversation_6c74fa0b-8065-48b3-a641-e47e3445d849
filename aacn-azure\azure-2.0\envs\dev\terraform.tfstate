{"version": 4, "terraform_version": "1.7.5", "serial": 25, "lineage": "c360ca42-b753-62aa-b6f4-9c5ec815d360", "outputs": {}, "resources": [{"module": "module.mgmt_groups", "mode": "managed", "type": "azurerm_management_group", "name": "landing_zones", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"display_name": "Landing_Zones", "id": "/providers/Microsoft.Management/managementGroups/AACN-landing_zone", "name": "AACN-landing_zone", "parent_management_group_id": "/providers/Microsoft.Management/managementGroups/AACN-2.0", "subscription_ids": ["21dc48e9-b2ef-4158-bc7b-7af56a4df1cb"], "tenant_scoped_id": "/tenants/ca4fd2d7-85ea-42d2-b7b3-30ef2666c7ab/providers/Microsoft.Management/managementGroups/AACN-landing_zone", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjoxODAwMDAwMDAwMDAwfX0=", "dependencies": ["module.mgmt_groups.azurerm_management_group.root"]}]}, {"module": "module.mgmt_groups", "mode": "managed", "type": "azurerm_management_group", "name": "platform", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"display_name": "Platform", "id": "/providers/Microsoft.Management/managementGroups/AACN-Plateform", "name": "AACN-Plateform", "parent_management_group_id": "/providers/Microsoft.Management/managementGroups/AACN-2.0", "subscription_ids": ["fce95a24-209c-4e69-98de-885660170e1d"], "tenant_scoped_id": "/tenants/ca4fd2d7-85ea-42d2-b7b3-30ef2666c7ab/providers/Microsoft.Management/managementGroups/AACN-Plateform", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjoxODAwMDAwMDAwMDAwfX0=", "dependencies": ["module.mgmt_groups.azurerm_management_group.root"]}]}, {"module": "module.mgmt_groups", "mode": "managed", "type": "azurerm_management_group", "name": "root", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"display_name": "AACN-2.0", "id": "/providers/Microsoft.Management/managementGroups/AACN-2.0", "name": "AACN-2.0", "parent_management_group_id": "/providers/Microsoft.Management/managementGroups/ca4fd2d7-85ea-42d2-b7b3-30ef2666c7ab", "subscription_ids": [], "tenant_scoped_id": "/tenants/ca4fd2d7-85ea-42d2-b7b3-30ef2666c7ab/providers/Microsoft.Management/managementGroups/AACN-2.0", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjoxODAwMDAwMDAwMDAwfX0="}]}, {"module": "module.mgmt_groups", "mode": "managed", "type": "azurerm_management_group", "name": "sandbox", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"display_name": "Sandbox", "id": "/providers/Microsoft.Management/managementGroups/AACN-Sandbox", "name": "AACN-Sandbox", "parent_management_group_id": "/providers/Microsoft.Management/managementGroups/AACN-2.0", "subscription_ids": ["067e726d-1a79-4cc3-aba1-7fb1398638b5"], "tenant_scoped_id": "/tenants/ca4fd2d7-85ea-42d2-b7b3-30ef2666c7ab/providers/Microsoft.Management/managementGroups/AACN-Sandbox", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMCwidXBkYXRlIjoxODAwMDAwMDAwMDAwfX0=", "dependencies": ["module.mgmt_groups.azurerm_management_group.root"]}]}], "check_results": null}