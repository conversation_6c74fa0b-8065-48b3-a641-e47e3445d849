# module "windows_vm" {
#   source = "../../../../modules/azure_vm"

#   resource_group_name     = var.resource_group_name
#   location                = var.location
#   vm_name                 = "vm-dev-test-01"
#   vm_size                 = "Standard_DS2_v2"
#   admin_username          = "azureuser"
#   admin_password          = var.admin_password
#   os_disk_type            = "Standard_LRS"
#   image_publisher         = "microsoftwindowsdesktop"
#   image_offer             = "windows-11"
#   image_sku               = "win11-23h2-pro"
#   image_version           = "latest"
#   subnet_id               = data.azurerm_subnet.snet.id
#   tags = {
#     environment = "dev"
#   }

#     nsg_rules = [
#     # {
#     #   name                       = "allow_rdp"
#     #   priority                   = 100
#     #   direction                  = "Inbound"
#     #   access                     = "Allow"
#     #   protocol                   = "Tcp"
#     #   source_port_range          = "*"
#     #   destination_port_range     = "3389"
#     #   source_address_prefix      = "********/24"
#     #   destination_address_prefix = "*"
#     # },
#     {
#       name                       = "allow_local"
#       priority                   = 102
#       direction                  = "Inbound"
#       access                     = "Allow"
#       protocol                   = "Tcp"
#       source_port_range          = "*"
#       destination_port_range     = "3389"
#       source_address_prefix      = "*************"
#       destination_address_prefix = "*"
#     }
#   ]

# }

# data "azurerm_subnet" "snet" {
#   name                 = "snet-devtest-PRODUCT-dev"
#   virtual_network_name = "vnet-westus2-devtest-dev"
#   resource_group_name  = "rg-devtest-connectivity-001"
# }