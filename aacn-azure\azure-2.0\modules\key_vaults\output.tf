output "key_vault_names" {
  description = "A list of Key Vault names"
  value       = [for kv in azurerm_key_vault.key_vault : kv.name]
}
output "key_vault_uris" {
  description = "A map of Key Vault URIs by name"
  value       = { for kv in azurerm_key_vault.key_vault : kv.name => kv.vault_uri }
}
output "key_vault_ids" {
  description = "The IDs of the created Key Vaults."
  value       = [for kv in azurerm_key_vault.key_vault : kv.id]
}