module "spoke_iland_vnet" {
  source = "../../../../modules/networking/spoke_vnet"

  vnet_name              = "vnet-production-iland-westus2-001"
  address_space          = ["********/16"]
  location               = var.location
  resource_group_name    = var.resource_group_name
  subnet_name            = "snet-production-iland-api"
  subnet_address_prefixes = ["********/24"]

  providers = {
    azurerm = azurerm.production
  }
}

# Outputs
output "spoke_iland_vnet_name" {
  value = module.spoke_iland_vnet.vnet_name
}

output "spoke_iland_vnet_id" {
  value = module.spoke_iland_vnet.vnet_id
}

output "spoke_iland_subnet_id" {
  value = module.spoke_iland_vnet.subnet_id
}