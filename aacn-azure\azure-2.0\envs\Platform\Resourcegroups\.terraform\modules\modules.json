{"Modules": [{"Key": "", "Source": "", "Dir": "."}, {"Key": "cloudpc", "Source": "../Resourcegroups/Import/rg-platform-cloudpc-westus2-001", "Dir": "../Resourcegroups/Import/rg-platform-cloudpc-westus2-001"}, {"Key": "cloudpc.rg-platform-cloudpc-westus2-001", "Source": "../../../../../modules/resource_group", "Dir": "../../../modules/resource_group"}, {"Key": "comms", "Source": "../Resourcegroups/Import/rg-platform-comms-westus2-001", "Dir": "../Resourcegroups/Import/rg-platform-comms-westus2-001"}, {"Key": "comms.rg-platform-comms-westus2-001", "Source": "../../../../../modules/resource_group", "Dir": "../../../modules/resource_group"}, {"Key": "connectivity", "Source": "../Resourcegroups/Import/rg-platform-connectivity-westus2-001", "Dir": "../Resourcegroups/Import/rg-platform-connectivity-westus2-001"}, {"Key": "connectivity.frontdoor", "Source": "../../../../../modules/frontdoor", "Dir": "../../../modules/frontdoor"}, {"Key": "connectivity.hub_network", "Source": "../../../../../modules/networking/hub_vnet", "Dir": "../../../modules/networking/hub_vnet"}, {"Key": "connectivity.nsg_bastion", "Source": "../../../../../modules/networking/nsg", "Dir": "../../../modules/networking/nsg"}, {"Key": "connectivity.nsg_cloudpcs_test", "Source": "../../../../../modules/networking/nsg", "Dir": "../../../modules/networking/nsg"}, {"Key": "connectivity.nsg_dns_inbound", "Source": "../../../../../modules/networking/nsg", "Dir": "../../../modules/networking/nsg"}, {"Key": "connectivity.nsg_dns_outbound", "Source": "../../../../../modules/networking/nsg", "Dir": "../../../modules/networking/nsg"}, {"Key": "connectivity.nsg_identity_dc", "Source": "../../../../../modules/networking/nsg", "Dir": "../../../modules/networking/nsg"}, {"Key": "connectivity.nsg_privatelink_storage", "Source": "../../../../../modules/networking/nsg", "Dir": "../../../modules/networking/nsg"}, {"Key": "connectivity.rg-platform-connectivity-westus2-001", "Source": "../../../../../modules/resource_group", "Dir": "../../../modules/resource_group"}, {"Key": "dept", "Source": "../Resourcegroups/Import/rg-platform-users-westus2-001-rg", "Dir": "../Resourcegroups/Import/rg-platform-users-westus2-001-rg"}, {"Key": "dept.dept_storage", "Source": "../../../../../modules/storage_account/st-nocontainer", "Dir": "../../../modules/storage_account/st-nocontainer"}, {"Key": "dept.rg-platform-users-westus2-001-rg", "Source": "../../../../../modules/resource_group", "Dir": "../../../modules/resource_group"}, {"Key": "dept.users_storage", "Source": "../../../../../modules/storage_account/st-nocontainer", "Dir": "../../../modules/storage_account/st-nocontainer"}, {"Key": "devops", "Source": "../Resourcegroups/Import/rg-platform-devops-centralus-001", "Dir": "../Resourcegroups/Import/rg-platform-devops-centralus-001"}, {"Key": "devops.rg-platform-devops-centralus-001", "Source": "../../../../../modules/resource_group", "Dir": "../../../modules/resource_group"}, {"Key": "honeypot", "Source": "../Resourcegroups/Import/rg-platform-honeypot-westus2-001", "Dir": "../Resourcegroups/Import/rg-platform-honeypot-westus2-001"}, {"Key": "identity", "Source": "../Resourcegroups/Import/rg-platform-identity-westus2-001", "Dir": "../Resourcegroups/Import/rg-platform-identity-westus2-001"}, {"Key": "identity.aadds_network", "Source": "../../../../../modules/networking/aadds", "Dir": "../../../modules/networking/aadds"}, {"Key": "identity.key_vaults", "Source": "../../../../../modules/key_vaults", "Dir": "../../../modules/key_vaults"}, {"Key": "identity.rg-platform-identity-westus2-001", "Source": "../../../../../modules/resource_group", "Dir": "../../../modules/resource_group"}, {"Key": "keyvaultmanager", "Source": "../Resourcegroups/Import/rg-platform-keyvaultmanager-westus2-001", "Dir": "../Resourcegroups/Import/rg-platform-keyvaultmanager-westus2-001"}, {"Key": "keyvaultmanager.keyvault_storage", "Source": "../../../../../modules/storage_account", "Dir": "../../../modules/storage_account"}, {"Key": "keyvaultmanager.rg-platform-keyvaultmanager-westus2-001", "Source": "../../../../../modules/resource_group", "Dir": "../../../modules/resource_group"}, {"Key": "mgmt", "Source": "../Resourcegroups/Import/rg-platform-mgmt-westus2-001", "Dir": "../Resourcegroups/Import/rg-platform-mgmt-westus2-001"}, {"Key": "mgmt.key_vaults", "Source": "../../../../../modules/key_vaults", "Dir": "../../../modules/key_vaults"}, {"Key": "mgmt.platform_storage", "Source": "../../../../../modules/storage_account", "Dir": "../../../modules/storage_account"}, {"Key": "mgmt.rg-platform-mgmt-westus2-001", "Source": "../../../../../modules/resource_group", "Dir": "../../../modules/resource_group"}, {"Key": "sent", "Source": "../Resourcegroups/Import/rg-platform-snet-westus2-001", "Dir": "../Resourcegroups/Import/rg-platform-snet-westus2-001"}, {"Key": "sent.rg-platform-snet-westus2-001", "Source": "../../../../../modules/resource_group", "Dir": "../../../modules/resource_group"}, {"Key": "sent.sentinel_storage", "Source": "../../../../../modules/storage_account", "Dir": "../../../modules/storage_account"}, {"Key": "testvm", "Source": "./Import/vm-platform-testvm-001", "Dir": "Import/vm-platform-testvm-001"}]}