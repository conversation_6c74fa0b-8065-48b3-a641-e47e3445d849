# variable "firewall_name" {
#   description = "The name of the Azure Firewall"
#   type        = string
# }

# variable "location" {
#   description = "Azure region"
#   type        = string
# }

# variable "resource_group_name" {
#   description = "Resource group for the firewall"
#   type        = string
# }

# variable "sku_name" {
#   description = "Azure Firewall SKU Name"
#   type        = string
# }

# variable "sku_tier" {
#   description = "Azure Firewall SKU Tier"
#   type        = string
# }

# variable "subnet_id" {
#   description = "The subnet ID where the firewall is deployed"
#   type        = string
# }

# variable "public_ip_id" {
#   description = "Public IP ID assigned to the firewall"
#   type        = string
# }

# variable "tags" {
#   description = "Tags for the firewall"
#   type        = map(string)
#   default     = {}
# }