variable "storage_account_name" {
  description = "The name of the storage account"
  type        = string
}

variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "location" {
  description = "The location of the storage account"
  type        = string
}

variable "tags" {
  type        = map(string)
  description = "The tags of the storage"
}

# variable "container_name" {
#   description = "The name of the storage container"
#   type        = string
# }