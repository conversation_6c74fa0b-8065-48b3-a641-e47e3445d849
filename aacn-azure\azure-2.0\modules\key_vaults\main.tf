resource "azurerm_key_vault" "key_vault" {
  for_each            = var.key_vault_names
  name                = each.key
  location            = var.location
  resource_group_name = var.resource_group_name
  sku_name            = var.sku
  tenant_id = var.tenant_id
  # Additional configurations can be added here
}

resource "azurerm_key_vault_access_policy" "access_policy" {
  for_each = { for idx, policy in var.access_policies : idx => policy }

  # key_vault_id = azurerm_key_vault.key_vault[policy.key_vault_name].id
  key_vault_id          = azurerm_key_vault.key_vault[each.value.key_vault_name].id
  
  tenant_id    = each.value.tenant_id
  object_id    = each.value.object_id
  secret_permissions = each.value.secret_permissions
  key_permissions    = each.value.key_permissions
  certificate_permissions = each.value.certificate_permissions  
  lifecycle {
    ignore_changes = [
      # Be more specific about what to ignore
      secret_permissions,
      key_permissions,
      certificate_permissions,
    ]
  }
}

