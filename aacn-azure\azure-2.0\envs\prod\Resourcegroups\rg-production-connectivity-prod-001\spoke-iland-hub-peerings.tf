# Peering from iLAND spoke to hub
resource "azurerm_virtual_network_peering" "spoke_iland_to_hub" {
  name                      = "iland-to-hub"
  resource_group_name       = var.resource_group_name
  virtual_network_name      = "vnet-production-iland-westus2-001"
  remote_virtual_network_id = data.terraform_remote_state.hub.outputs.hub_vnet_id
  allow_virtual_network_access = true
  use_remote_gateways      = true  # Enable to use the VPN gateway in the hub
}

# Remote state to get hub vnet details
# data "terraform_remote_state" "iland_hub" {
#   backend = "azurerm"
#   config = {
#     storage_account_name = "stplatformterra001"  
#     container_name       = "tfstate"               
#     key                  = "platform.terraform.tfstate" 
#     resource_group_name  = "rg-platform-mgmt-westus2-001"             
#     subscription_id      = "fce95a24-209c-4e69-98de-885660170e1d"
#   }
# }


# resource "azurerm_virtual_network_peering" "spoke_iland_to_Azure1_prod" {
#   name                      = "spoke_iland_to_Azure1_prod"
#   resource_group_name       = var.resource_group_name
#   virtual_network_name      = "vnet-production-iland-westus2-001"
#   remote_virtual_network_id = "/subscriptions/7daa1d35-dc46-4683-9c8c-b175bf57817a/resourceGroups/rg-westus2-network-prod-001/providers/Microsoft.Network/virtualNetworks/vnet-westus2-platform-hub-prod"
#   allow_virtual_network_access = true
#   use_remote_gateways      = false  # Enable to use the VPN gateway in the hub
# }

resource "azurerm_virtual_network_peering" "spoke_iland_to_Azure1_iland" {
  name                      = "spoke_iland_to_Azure1_iland"
  resource_group_name       = var.resource_group_name
  virtual_network_name      = "vnet-production-iland-westus2-001"
  remote_virtual_network_id = "/subscriptions/7daa1d35-dc46-4683-9c8c-b175bf57817a/resourceGroups/rg-westus2-network-iland-001/providers/Microsoft.Network/virtualNetworks/vnet-westus2-iland-001"
  allow_virtual_network_access = true
  use_remote_gateways      = false  # Enable to use the VPN gateway in the hub
}