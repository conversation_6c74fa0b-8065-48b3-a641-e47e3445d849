variable "storage_account_name" {
  type    = string
  default = "stkeyvaultmanager001"
}

variable "resource_group_name" {
  default = "rg-platform-keyvaultmanager-westus2-001"
}

variable "location" {
  default = "West US 2"
}

variable "subscription_id" {
  default = "fce95a24-209c-4e69-98de-885660170e1d"
}

variable "resource_group_tags" {
  type = map(string)
  default = {
    environment = "Platform"
    CostCenter  = "1911-51210"
  }
}

variable "key_vaultname" {
  default = "stkeyvaultmanager001"
}

variable "tenant_id" {
  default = "ca4fd2d7-85ea-42d2-b7b3-30ef2666c7ab"
}

variable "sku" {
  default = "standard"
}

variable "key_vaults_tags" {
  type = map(string)
  default = {
    environment = "Platform"
    # CostCenter  = "1911-51210"
  }
}