module "cloudpc" {
  source = "../Resourcegroups/Import/rg-platform-cloudpc-westus2-001"
}
module "comms" {
  source = "../Resourcegroups/Import/rg-platform-comms-westus2-001"
}
module "connectivity" {
  source = "../Resourcegroups/Import/rg-platform-connectivity-westus2-001"
}
module "devops" {
  source = "../Resourcegroups/Import/rg-platform-devops-centralus-001"
}
module "honeypot" {
  source = "../Resourcegroups/Import/rg-platform-honeypot-westus2-001"
}
module "identity" {
  source = "../Resourcegroups/Import/rg-platform-identity-westus2-001"
}
module "keyvaultmanager" {
  source = "../Resourcegroups/Import/rg-platform-keyvaultmanager-westus2-001"
}
module "mgmt" {
  source = "../Resourcegroups/Import/rg-platform-mgmt-westus2-001"
}

module "testvm" {
  source = "./Import/vm-platform-testvm-001"
}
module "sent" {
  source = "../Resourcegroups/Import/rg-platform-snet-westus2-001"
}

module "dept" {
  source = "../Resourcegroups/Import/rg-platform-users-westus2-001-rg"
}