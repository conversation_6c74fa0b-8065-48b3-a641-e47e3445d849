# Network Security Groups using the module
module "nsg_bastion" {
  source              = "../../../../../modules/networking/nsg"
  nsg_name            = "nsg-snet-bastion"
  location            = var.location
  resource_group_name = var.resource_group_name
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

module "nsg_cloudpcs_test" {
  source              = "../../../../../modules/networking/nsg"
  nsg_name            = "nsg-snet-cloudpcs-test"
  location            = var.location
  resource_group_name = var.resource_group_name
}

module "nsg_dns_inbound" {
  source              = "../../../../../modules/networking/nsg"
  nsg_name            = "nsg-snet-dns-inbound"
  location            = var.location
  resource_group_name = var.resource_group_name
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

module "nsg_dns_outbound" {
  source              = "../../../../../modules/networking/nsg"
  nsg_name            = "nsg-snet-dns-outbound"
  location            = var.location
  resource_group_name = var.resource_group_name
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

module "nsg_identity_dc" {
  source              = "../../../../../modules/networking/nsg"
  nsg_name            = "nsg-snet-identity-dc"
  location            = var.location
  resource_group_name = var.resource_group_name
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

module "nsg_privatelink_storage" {
  source              = "../../../../../modules/networking/nsg"
  nsg_name            = "nsg-snet-privatelink-storage"
  location            = var.location
  resource_group_name = var.resource_group_name
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

# Direct resources without modules
# Bastion Host
resource "azurerm_public_ip" "bastion_pip" {
  name                = "pip-bast-platform-hub-westus2-001"
  location            = var.location
  resource_group_name = var.resource_group_name
  allocation_method   = "Static"
  sku                 = "Standard"
  zones               = ["1", "2", "3"]
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

resource "azurerm_bastion_host" "bastion" {
  name                = "bast-platform-hub-westus2-001"
  location            = var.location
  resource_group_name = var.resource_group_name
  sku                 = "Standard"
  # zones               = ["1", "2"]
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }

  ip_configuration {
    name                 = "IpConf"
    subnet_id            = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/AzureBastionSubnet"
    public_ip_address_id = azurerm_public_ip.bastion_pip.id
  }
}

# DNS Resources
resource "azurerm_private_dns_resolver" "resolver" {
  name                = "dnspr-platform-aacnorg-001"
  location            = var.location
  resource_group_name = var.resource_group_name
  virtual_network_id  = data.azurerm_virtual_network.hub_vnet.id
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

# You need to define this resource separately
resource "azurerm_private_dns_resolver_outbound_endpoint" "outbound_endpoint" {
  name                    = "dnsprend-aacnorg-001"
  private_dns_resolver_id = azurerm_private_dns_resolver.resolver.id
  location                = var.location
  subnet_id               = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/snet-dns-outbound"
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

resource "azurerm_private_dns_resolver_dns_forwarding_ruleset" "ruleset" {
  name                                       = "dnsprend-ruleset-aacnorg"
  location                                   = var.location
  resource_group_name                        = var.resource_group_name
  private_dns_resolver_outbound_endpoint_ids = [azurerm_private_dns_resolver_outbound_endpoint.outbound_endpoint.id]
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

# Assuming this resource already exists or will be created separately
resource "azurerm_private_dns_zone" "dns_zone" {
  name                = "aacn.org"
  resource_group_name = var.resource_group_name
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

resource "azurerm_private_dns_zone_virtual_network_link" "vnet_link" {
  name                  = "link-aacn.org-to-platform-hub-westus2-001"
  private_dns_zone_name = azurerm_private_dns_zone.dns_zone.name
  resource_group_name   = var.resource_group_name
  virtual_network_id    = data.azurerm_virtual_network.hub_vnet.id
  registration_enabled  = true
}

# Data source for the hub VNet
data "azurerm_virtual_network" "hub_vnet" {
  name                = "vnet-platform-hub-westus2-001"
  resource_group_name = var.resource_group_name
}