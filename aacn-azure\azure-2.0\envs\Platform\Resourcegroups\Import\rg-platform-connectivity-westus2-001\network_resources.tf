# Network Security Groups using the module
module "nsg_bastion" {
  source              = "../../../../../modules/networking/nsg"
  nsg_name            = "nsg-snet-bastion"
  location            = var.location
  resource_group_name = var.resource_group_name
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

module "nsg_cloudpcs_test" {
  source              = "../../../../../modules/networking/nsg"
  nsg_name            = "nsg-snet-cloudpcs-test"
  location            = var.location
  resource_group_name = var.resource_group_name
}

module "nsg_dns_inbound" {
  source              = "../../../../../modules/networking/nsg"
  nsg_name            = "nsg-snet-dns-inbound"
  location            = var.location
  resource_group_name = var.resource_group_name
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

module "nsg_dns_outbound" {
  source              = "../../../../../modules/networking/nsg"
  nsg_name            = "nsg-snet-dns-outbound"
  location            = var.location
  resource_group_name = var.resource_group_name
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

module "nsg_identity_dc" {
  source              = "../../../../../modules/networking/nsg"
  nsg_name            = "nsg-snet-identity-dc"
  location            = var.location
  resource_group_name = var.resource_group_name
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

module "nsg_privatelink_storage" {
  source              = "../../../../../modules/networking/nsg"
  nsg_name            = "nsg-snet-privatelink-storage"
  location            = var.location
  resource_group_name = var.resource_group_name
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

# Direct resources without modules
# Bastion Host
resource "azurerm_public_ip" "bastion_pip" {
  name                = "pip-bast-platform-hub-westus2-001"
  location            = var.location
  resource_group_name = var.resource_group_name
  allocation_method   = "Static"
  sku                 = "Standard"
  zones               = ["1", "2", "3"]
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

resource "azurerm_bastion_host" "bastion" {
  name                = "bast-platform-hub-westus2-001"
  location            = var.location
  resource_group_name = var.resource_group_name
  sku                 = "Standard"
  # zones               = ["1", "2"]
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }

  ip_configuration {
    name                 = "IpConf"
    subnet_id            = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/AzureBastionSubnet"
    public_ip_address_id = azurerm_public_ip.bastion_pip.id
  }

  # Additional Bastion configuration to match export
  copy_paste_enabled     = true
  file_copy_enabled      = false
  ip_connect_enabled     = false
  kerberos_enabled       = false
  scale_units            = 2
  shareable_link_enabled = false
  tunneling_enabled      = false
}

# DNS Resources
resource "azurerm_private_dns_resolver" "resolver" {
  name                = "dnspr-platform-aacnorg-001"
  location            = var.location
  resource_group_name = var.resource_group_name
  virtual_network_id  = data.azurerm_virtual_network.hub_vnet.id
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

# You need to define this resource separately
resource "azurerm_private_dns_resolver_outbound_endpoint" "outbound_endpoint" {
  name                    = "dnsprend-aacnorg-outbound"
  private_dns_resolver_id = azurerm_private_dns_resolver.resolver.id
  location                = var.location
  subnet_id               = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/snet-dns-outbound"
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

# DNS Inbound Endpoint
resource "azurerm_private_dns_resolver_inbound_endpoint" "inbound_endpoint" {
  name                    = "dnsprend-aacnorg-inbound"
  private_dns_resolver_id = azurerm_private_dns_resolver.resolver.id
  location                = var.location
  ip_configurations {
    private_ip_allocation_method = "Dynamic"
    subnet_id                    = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/snet-dns-inbound"
  }
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

# DNS Forwarding Rule
resource "azurerm_private_dns_resolver_forwarding_rule" "aacn_rule" {
  name                      = "dnsprend-rule-aacnorg-to-azureaacnorg"
  dns_forwarding_ruleset_id = azurerm_private_dns_resolver_dns_forwarding_ruleset.ruleset.id
  domain_name               = "aacn.org."
  enabled                   = true
  target_dns_servers {
    ip_address = "********"
    port       = 53
  }
  target_dns_servers {
    ip_address = "********"
    port       = 53
  }
}

# DNS VNet Link
resource "azurerm_private_dns_resolver_virtual_network_link" "vnet_link" {
  name                      = "vnet-platform-hub-westus2-001-link"
  dns_forwarding_ruleset_id = azurerm_private_dns_resolver_dns_forwarding_ruleset.ruleset.id
  virtual_network_id        = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001"
}

# Additional Subnets from Export
resource "azurerm_subnet" "bastion_subnet" {
  name                 = "AzureBastionSubnet"
  resource_group_name  = var.resource_group_name
  virtual_network_name = "vnet-platform-hub-westus2-001"
  address_prefixes     = ["********/24"]
}

resource "azurerm_subnet" "cloudpcs_test_subnet" {
  name                 = "snet-cloudpcs-test"
  resource_group_name  = var.resource_group_name
  virtual_network_name = "vnet-platform-hub-westus2-001"
  address_prefixes     = ["********/24"]
}

resource "azurerm_subnet" "dns_inbound_subnet" {
  name                 = "snet-dns-inbound"
  resource_group_name  = var.resource_group_name
  virtual_network_name = "vnet-platform-hub-westus2-001"
  address_prefixes     = ["********/24"]
}

resource "azurerm_subnet" "dns_outbound_subnet" {
  name                 = "snet-dns-outbound"
  resource_group_name  = var.resource_group_name
  virtual_network_name = "vnet-platform-hub-westus2-001"
  address_prefixes     = ["********/24"]
}

resource "azurerm_subnet" "privatelink_storage_subnet" {
  name                 = "snet-privatelink-storage"
  resource_group_name  = var.resource_group_name
  virtual_network_name = "vnet-platform-hub-westus2-001"
  address_prefixes     = ["********/24"]
}

# Route Table
resource "azurerm_route_table" "testdb_poc" {
  name                = "rt-testdb-poc"
  location            = var.location
  resource_group_name = var.resource_group_name
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

# Route Table Association
resource "azurerm_subnet_route_table_association" "testdb_poc_association" {
  subnet_id      = azurerm_subnet.cloudpcs_test_subnet.id
  route_table_id = azurerm_route_table.testdb_poc.id
}

resource "azurerm_private_dns_resolver_dns_forwarding_ruleset" "ruleset" {
  name                                       = "dnsprend-ruleset-aacnorg"
  location                                   = var.location
  resource_group_name                        = var.resource_group_name
  private_dns_resolver_outbound_endpoint_ids = [azurerm_private_dns_resolver_outbound_endpoint.outbound_endpoint.id]
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

# Assuming this resource already exists or will be created separately
resource "azurerm_private_dns_zone" "dns_zone" {
  name                = "aacn.org"
  resource_group_name = var.resource_group_name
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

# resource "azurerm_private_dns_zone_virtual_network_link" "vnet_link" {
#   name                  = "link-aacn.org-to-platform-hub"
#   private_dns_zone_name = azurerm_private_dns_zone.dns_zone.name
#   resource_group_name   = var.resource_group_name
#   virtual_network_id    = data.azurerm_virtual_network.hub_vnet.id
#   registration_enabled  = true
# }

# Data source for the hub VNet
data "azurerm_virtual_network" "hub_vnet" {
  name                = "vnet-platform-hub-westus2-001"
  resource_group_name = var.resource_group_name
}