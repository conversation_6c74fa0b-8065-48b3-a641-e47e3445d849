variable "resource_group_name" {
  default = "rg-platform-mgmt-westus2-001"
}

variable "location" {
  default = "West US 2"
}

variable "subscription_id" {
  default = "fce95a24-209c-4e69-98de-885660170e1d"
}

variable "resource_group_tags" {
  type = map(string)
  default = {
    environment = "Platform"
    CostCenter  = "1911-51210"
  }
}

variable "storage_account_name" {
  type    = string
  default = "stplatformterra001"
}

# variable "key_vaultname" {
#   default = "kv-platform-terra-001"
# }

# variable "key_vaultname" {
#   description = "List of Key Vault names to create"
#   type        = list(string)
#   default     = ["kv-platform-terra-001"]
# }

variable "tenant_id" {
  default = "ca4fd2d7-85ea-42d2-b7b3-30ef2666c7ab"
}

variable "sku" {
  default = "standard"
}

variable "key_vaults_tags" {
  type = map(string)
  default = {
    environment = "Platform"
    # CostCenter  = "1911-51210"
  }
}

# variable "devops_leads_object_id" {
#   type        = string
#   description = "Object ID for the DevOps Leads group"
#   default = "5866b75a-2207-4212-ad24-3b725610b2a1"
# }

# variable "dynatech_engineers_object_id" {
#   type        = string
#   description = "Object ID for the DynaTech Engineers group"
#   default = "485da724-018a-4d86-8aee-adf46326de2f"
# }

# variable "aacn_dps_it_operations_object_id" {
#   type        = string
#   description = "Object ID for the AACN-DPS-IT Operations application"
#   default = "45f280a8-4460-4ac1-bcbd-ca26d65a679e"
# }

variable "access_policy_object_ids" {
  description = "List of object IDs for access policies"
  type        = list(string)
  default     = [
    "5866b75a-2207-4212-ad24-3b725610b2a1",
    "485da724-018a-4d86-8aee-adf46326de2f",
    "45f280a8-4460-4ac1-bcbd-ca26d65a679e",
  ]
}

variable "secret_permissions" {
  description = "Default secret permissions for Key Vault access policy"
  type        = list(string)
  default     = ["Get", "List" , "Delete", "Recover", "Backup", "Restore", "Purge", "Set"]
}

variable "key_permissions" {
  description = "Default key permissions for Key Vault access policy"
  type        = list(string)
  default     = ["Get", "List" , "Update", "Create", "Import", "Delete", "Recover", "Backup", "Restore", "Decrypt", "Encrypt", "Purge", "Sign", "UnwrapKey", "Verify", "WrapKey"]
}

variable "certificate_permissions" {
  description = "Default certificate permissions for Key Vault access policy"
  type        = list(string)
  default     = ["Get", "List" , "Update", "Create", "Import", "Delete", "Recover", "Backup", "Restore", "DeleteIssuers", "GetIssuers", "ListIssuers", "ManageContacts", "ManageIssuers", "SetIssuers"]
}


###################################### Microsoft Sentinel ##########################################

variable "log_analytics_workspace_name" {
  description = "The name of the Log Analytics Workspace"
  type        = string
  default     = "work-platform-mgmt-001"
}

# Log Analytics Workspace SKU
variable "log_analytics_workspace_sku" {
  description = "The SKU for the Log Analytics Workspace (e.g., PerGB2018 for Pay-As-You-Go)"
  type        = string
  default     = "PerGB2018"
}

# Log Analytics Workspace Retention
variable "log_analytics_retention_in_days" {
  description = "Data retention period for the Log Analytics Workspace in days"
  type        = number
  default     = 30
}

variable "sentinel_storage_account_name" {
  type    = string
  default = "stplatmgmt001"
}
