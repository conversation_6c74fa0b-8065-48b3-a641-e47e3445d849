variable "resource_group_name" {
  default = "rg-platform-sent-westus2-001"
}

variable "location" {
  default = "West US 2"
}

variable "subscription_id" {
  default = "fce95a24-209c-4e69-98de-885660170e1d"
}

variable "resource_group_tags" {
  type = map(string)
  default = {
    environment = "Platform"
    CostCenter  = "1911-51210"
  }
}

# variable "storage_account_name" {
#   type    = string
#   default = "stplatformterra001"
# }



variable "tenant_id" {
  default = "ca4fd2d7-85ea-42d2-b7b3-30ef2666c7ab"
}

variable "sku" {
  default = "standard"
}

###################################### Microsoft Sentinel ##########################################

variable "log_analytics_workspace_name" {
  description = "The name of the Log Analytics Workspace"
  type        = string
  default     = "work-platform-sent-001"
}

# # Log Analytics Workspace SKU
variable "log_analytics_workspace_sku" {
  description = "The SKU for the Log Analytics Workspace (e.g., PerGB2018 for Pay-As-You-Go)"
  type        = string
  default     = "PerGB2018"
}

# # Log Analytics Workspace Retention
variable "log_analytics_retention_in_days" {
  description = "Data retention period for the Log Analytics Workspace in days"
  type        = number
  default     = 30
}

variable "sentinel_storage_account_name" {
  type    = string
  default = "stplatsent001"
}
