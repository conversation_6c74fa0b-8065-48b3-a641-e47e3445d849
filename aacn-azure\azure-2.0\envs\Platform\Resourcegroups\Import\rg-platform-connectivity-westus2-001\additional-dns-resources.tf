# Additional DNS Resources from Export

# Private DNS Zone for azure.aacn.org
resource "azurerm_private_dns_zone" "azure_aacn_org" {
  name                = "azure.aacn.org"
  resource_group_name = var.resource_group_name
  tags = {
    CostCenter  = "1911-51210"
    Environment = "prod"
  }
}

# DNS A Records
resource "azurerm_private_dns_a_record" "vm_aacn_prod_001" {
  name                = "vm-aacn-prod-001"
  zone_name           = azurerm_private_dns_zone.azure_aacn_org.name
  resource_group_name = var.resource_group_name
  ttl                 = 3600
  records             = ["10.2.0.4"]
}

resource "azurerm_private_dns_a_record" "vm_aacn_prod_002" {
  name                = "vm-aacn-prod-002"
  zone_name           = azurerm_private_dns_zone.azure_aacn_org.name
  resource_group_name = var.resource_group_name
  ttl                 = 3600
  records             = ["10.2.0.5"]
}

resource "azurerm_private_dns_a_record" "vm_aacn_prod_003" {
  name                = "vm-aacn-prod-003"
  zone_name           = azurerm_private_dns_zone.azure_aacn_org.name
  resource_group_name = var.resource_group_name
  ttl                 = 3600
  records             = ["10.2.0.6"]
}

resource "azurerm_private_dns_a_record" "vm_aacn_prod_004" {
  name                = "vm-aacn-prod-004"
  zone_name           = azurerm_private_dns_zone.azure_aacn_org.name
  resource_group_name = var.resource_group_name
  ttl                 = 3600
  records             = ["10.2.0.7"]
}

resource "azurerm_private_dns_a_record" "vm_aacn_prod_005" {
  name                = "vm-aacn-prod-005"
  zone_name           = azurerm_private_dns_zone.azure_aacn_org.name
  resource_group_name = var.resource_group_name
  ttl                 = 3600
  records             = ["10.2.0.8"]
}

resource "azurerm_private_dns_a_record" "vm_aacn_prod_006" {
  name                = "vm-aacn-prod-006"
  zone_name           = azurerm_private_dns_zone.azure_aacn_org.name
  resource_group_name = var.resource_group_name
  ttl                 = 3600
  records             = ["********"]
}

# Azure DNS Zone VNet Link
resource "azurerm_private_dns_zone_virtual_network_link" "azure_aacn_vnet_link" {
  name                  = "link-azureaacnorg-to-platform-hub"
  resource_group_name   = var.resource_group_name
  private_dns_zone_name = azurerm_private_dns_zone.azure_aacn_org.name
  virtual_network_id    = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001"
  registration_enabled  = true
}
