output "lb_id" {
  description = "ID of the created load balancer"
  value       = azurerm_lb.aadds_lb.id
}

output "lb_frontend_ip_configuration" {
  description = "Frontend IP configuration of the load balancer"
  value       = azurerm_lb.aadds_lb.frontend_ip_configuration[0].name
}

output "backend_address_pool_id" {
  description = "ID of the backend address pool"
  value       = azurerm_lb_backend_address_pool.aadds_backend_pool.id
}

output "public_ip_id" {
  description = "ID of the public IP address"
  value       = azurerm_public_ip.aadds_pip.id
}

output "public_ip_address" {
  description = "The public IP address"
  value       = azurerm_public_ip.aadds_pip.ip_address
}

output "nic_1_id" {
  description = "ID of the first network interface"
  value       = azurerm_network_interface.aadds_nic_1.id
}

output "nic_2_id" {
  description = "ID of the second network interface"
  value       = azurerm_network_interface.aadds_nic_2.id
}

output "nic_1_private_ip" {
  description = "Private IP address of the first network interface"
  value       = azurerm_network_interface.aadds_nic_1.ip_configuration[0].private_ip_address
}

output "nic_2_private_ip" {
  description = "Private IP address of the second network interface"
  value       = azurerm_network_interface.aadds_nic_2.ip_configuration[0].private_ip_address
}