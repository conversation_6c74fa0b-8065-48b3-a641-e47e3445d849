{"version": 4, "terraform_version": "1.7.5", "serial": 24, "lineage": "7901080b-9f86-f37e-7ad5-058cb0de7f73", "outputs": {}, "resources": [{"module": "module.rg-production-IIS-prod-001.module.rg-production-IIS-prod-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-production-IIS-prod-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-IIS-prod-001", "location": "westus2", "managed_by": "", "name": "rg-production-IIS-prod-001", "tags": {"environment": "prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-production-aacnorg-prod-001.module.rg-production-aacnorg-prod-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-production-aacnorg-prod-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-aacnorg-prod-001", "location": "westus2", "managed_by": "", "name": "rg-production-aacnorg-prod-001", "tags": {"environment": "prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-production-assessments-prod-001.module.rg-production-assessments-prod-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-production-assessments-prod-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-assessments-prod-001", "location": "westus2", "managed_by": "", "name": "rg-production-assessments-prod-001", "tags": {"environment": "prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-production-cert-cequeue-prod-001.module.rg-production-cert-cequeue-prod-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-production-cert-cequeue-prod-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-cert-cequeue-prod-001", "location": "westus2", "managed_by": "", "name": "rg-production-cert-cequeue-prod-001", "tags": {"environment": "prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-production-cert-certtestcenter-prod-001.module.rg-production-cert-certtestcenter-prod-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-production-cert-certtestcenter-prod-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-cert-certtestcenter-prod-001", "location": "westus2", "managed_by": "", "name": "rg-production-cert-certtestcenter-prod-001", "tags": {"environment": "prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-production-chapters-nursingnerwork-prod-001.module.rg-production-chapters-nursingnerwork-prod-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-production-chapters-nursingnerwork-prod-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-chapters-nursingnerwork-prod-001", "location": "westus2", "managed_by": "", "name": "rg-production-chapters-nursingnerwork-prod-001", "tags": {"environment": "prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-production-connectivity-prod-001.module.rg-production-connectivity-prod-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-production-connectivity-prod-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-connectivity-prod-001", "location": "westus2", "managed_by": "", "name": "rg-production-connectivity-prod-001", "tags": {"environment": "prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-production-hwe-hweatteams-prod-001.module.rg-production-hwe-hweatteams-prod-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-production-hwe-hweatteams-prod-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-hwe-hweatteams-prod-001", "location": "westus2", "managed_by": "", "name": "rg-production-hwe-hweatteams-prod-001", "tags": {"environment": "prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-production-learning-courses-prod-001.module.rg-production-learning-courses-prod-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-production-learning-courses-prod-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-learning-courses-prod-001", "location": "westus2", "managed_by": "", "name": "rg-production-learning-courses-prod-001", "tags": {"environment": "prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-production-lighthouse-DW-prod-001.module.rg-production-lighthouse-DW-prod-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-production-lighthouse-DW-prod-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-lighthouse-DW-prod-001", "location": "westus2", "managed_by": "", "name": "rg-production-lighthouse-DW-prod-001", "tags": {"environment": "prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-production-lighthouse-fabric-prod-001.module.rg-production-lighthouse-fabric-prod-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-production-lighthouse-fabric-prod-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-lighthouse-fabric-prod-001", "location": "westus2", "managed_by": "", "name": "rg-production-lighthouse-fabric-prod-001", "tags": {"environment": "prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-production-lighthouse-taxonomy-prod-001.module.rg-production-lighthouse-taxonomy-prod-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-production-lighthouse-taxonomy-prod-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-lighthouse-taxonomy-prod-001", "location": "westus2", "managed_by": "", "name": "rg-production-lighthouse-taxonomy-prod-001", "tags": {"environment": "prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-production-mgmt-westus2-001.module.platform_storage", "mode": "managed", "type": "azurerm_storage_account", "name": "storage", "provider": "module.rg-production-mgmt-westus2-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 4, "attributes": {"access_tier": "Hot", "account_kind": "StorageV2", "account_replication_type": "LRS", "account_tier": "Standard", "allow_nested_items_to_be_public": true, "allowed_copy_scope": "", "azure_files_authentication": [], "blob_properties": [{"change_feed_enabled": false, "change_feed_retention_in_days": 0, "container_delete_retention_policy": [], "cors_rule": [], "default_service_version": "", "delete_retention_policy": [], "last_access_time_enabled": false, "restore_policy": [], "versioning_enabled": false}], "cross_tenant_replication_enabled": true, "custom_domain": [], "customer_managed_key": [], "default_to_oauth_authentication": false, "dns_endpoint_type": "Standard", "edge_zone": "", "enable_https_traffic_only": true, "id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-mgmt-westus2-001/providers/Microsoft.Storage/storageAccounts/stprodterra001", "identity": [], "immutability_policy": [], "infrastructure_encryption_enabled": false, "is_hns_enabled": false, "large_file_share_enabled": null, "local_user_enabled": true, "location": "westus2", "min_tls_version": "TLS1_2", "name": "stprodterra001", "network_rules": [{"bypass": ["AzureServices"], "default_action": "Allow", "ip_rules": [], "private_link_access": [], "virtual_network_subnet_ids": []}], "nfsv3_enabled": false, "primary_access_key": "****************************************************************************************", "primary_blob_connection_string": "DefaultEndpointsProtocol=https;BlobEndpoint=https://stprodterra001.blob.core.windows.net/;AccountName=stprodterra001;AccountKey=****************************************************************************************", "primary_blob_endpoint": "https://stprodterra001.blob.core.windows.net/", "primary_blob_host": "stprodterra001.blob.core.windows.net", "primary_blob_internet_endpoint": "", "primary_blob_internet_host": "", "primary_blob_microsoft_endpoint": "", "primary_blob_microsoft_host": "", "primary_connection_string": "DefaultEndpointsProtocol=https;AccountName=stprodterra001;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "primary_dfs_endpoint": "https://stprodterra001.dfs.core.windows.net/", "primary_dfs_host": "stprodterra001.dfs.core.windows.net", "primary_dfs_internet_endpoint": "", "primary_dfs_internet_host": "", "primary_dfs_microsoft_endpoint": "", "primary_dfs_microsoft_host": "", "primary_file_endpoint": "https://stprodterra001.file.core.windows.net/", "primary_file_host": "stprodterra001.file.core.windows.net", "primary_file_internet_endpoint": "", "primary_file_internet_host": "", "primary_file_microsoft_endpoint": "", "primary_file_microsoft_host": "", "primary_location": "westus2", "primary_queue_endpoint": "https://stprodterra001.queue.core.windows.net/", "primary_queue_host": "stprodterra001.queue.core.windows.net", "primary_queue_microsoft_endpoint": "", "primary_queue_microsoft_host": "", "primary_table_endpoint": "https://stprodterra001.table.core.windows.net/", "primary_table_host": "stprodterra001.table.core.windows.net", "primary_table_microsoft_endpoint": "", "primary_table_microsoft_host": "", "primary_web_endpoint": "https://stprodterra001.z5.web.core.windows.net/", "primary_web_host": "stprodterra001.z5.web.core.windows.net", "primary_web_internet_endpoint": "", "primary_web_internet_host": "", "primary_web_microsoft_endpoint": "", "primary_web_microsoft_host": "", "public_network_access_enabled": true, "queue_encryption_key_type": "Service", "queue_properties": [{"cors_rule": [], "hour_metrics": [{"enabled": true, "include_apis": true, "retention_policy_days": 7, "version": "1.0"}], "logging": [{"delete": false, "read": false, "retention_policy_days": 0, "version": "1.0", "write": false}], "minute_metrics": [{"enabled": false, "include_apis": false, "retention_policy_days": 0, "version": "1.0"}]}], "resource_group_name": "rg-production-mgmt-westus2-001", "routing": [], "sas_policy": [], "secondary_access_key": "****************************************************************************************", "secondary_blob_connection_string": "", "secondary_blob_endpoint": null, "secondary_blob_host": null, "secondary_blob_internet_endpoint": null, "secondary_blob_internet_host": null, "secondary_blob_microsoft_endpoint": null, "secondary_blob_microsoft_host": null, "secondary_connection_string": "DefaultEndpointsProtocol=https;AccountName=stprodterra001;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "secondary_dfs_endpoint": null, "secondary_dfs_host": null, "secondary_dfs_internet_endpoint": null, "secondary_dfs_internet_host": null, "secondary_dfs_microsoft_endpoint": null, "secondary_dfs_microsoft_host": null, "secondary_file_endpoint": null, "secondary_file_host": null, "secondary_file_internet_endpoint": null, "secondary_file_internet_host": null, "secondary_file_microsoft_endpoint": null, "secondary_file_microsoft_host": null, "secondary_location": "", "secondary_queue_endpoint": null, "secondary_queue_host": null, "secondary_queue_microsoft_endpoint": null, "secondary_queue_microsoft_host": null, "secondary_table_endpoint": null, "secondary_table_host": null, "secondary_table_microsoft_endpoint": null, "secondary_table_microsoft_host": null, "secondary_web_endpoint": null, "secondary_web_host": null, "secondary_web_internet_endpoint": null, "secondary_web_internet_host": null, "secondary_web_microsoft_endpoint": null, "secondary_web_microsoft_host": null, "sftp_enabled": false, "share_properties": [{"cors_rule": [], "retention_policy": [{"days": 7}], "smb": []}], "shared_access_key_enabled": true, "static_website": [], "table_encryption_key_type": "Service", "tags": {"environment": "Prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************************************************************************"}]}, {"module": "module.rg-production-mgmt-westus2-001.module.platform_storage", "mode": "managed", "type": "azurerm_storage_container", "name": "tfstate", "provider": "module.rg-production-mgmt-westus2-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 1, "attributes": {"container_access_type": "private", "default_encryption_scope": "$account-encryption-key", "encryption_scope_override_enabled": true, "has_immutability_policy": false, "has_legal_hold": false, "id": "https://stprodterra001.blob.core.windows.net/tfstate", "metadata": {}, "name": "tfstate", "resource_manager_id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-mgmt-westus2-001/providers/Microsoft.Storage/storageAccounts/stprodterra001/blobServices/default/containers/tfstate", "storage_account_name": "stprodterra001", "timeouts": null}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["module.rg-production-mgmt-westus2-001.module.platform_storage.azurerm_storage_account.storage"]}]}, {"module": "module.rg-production-mgmt-westus2-001.module.rg-production-mgmt-westus2-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-production-mgmt-westus2-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-mgmt-westus2-001", "location": "westus2", "managed_by": "", "name": "rg-production-mgmt-westus2-001", "tags": {"environment": "Prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-production-netforum-prod-001.module.rg-production-netforum-prod-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-production-netforum-prod-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-netforum-prod-001", "location": "westus2", "managed_by": "", "name": "rg-production-netforum-prod-001", "tags": {"environment": "prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-production-nti-eventsv3-prod-001.module.rg-production-nti-eventsv3-prod-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-production-nti-eventsv3-prod-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-nti-eventsv3-prod-001", "location": "westus2", "managed_by": "", "name": "rg-production-nti-eventsv3-prod-001", "tags": {"environment": "prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-production-nti-programapproval-prod-001.module.rg-production-nti-programapproval-prod-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-production-nti-programapproval-prod-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-nti-programapproval-prod-001", "location": "westus2", "managed_by": "", "name": "rg-production-nti-programapproval-prod-001", "tags": {"environment": "prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-production-nursing-beaconsurvey-prod-001.module.rg-production-nursing-beaconsurvey-prod-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-production-nursing-beaconsurvey-prod-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-nursing-beaconsurvey-prod-001", "location": "westus2", "managed_by": "", "name": "rg-production-nursing-beaconsurvey-prod-001", "tags": {"environment": "prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-production-nursing-beaconv3-prod-001.module.rg-production-nursing-beaconv3-prod-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-production-nursing-beaconv3-prod-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-nursing-beaconv3-prod-001", "location": "westus2", "managed_by": "", "name": "rg-production-nursing-beaconv3-prod-001", "tags": {"environment": "prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.rg-production-shared-prod-001.module.rg-production-shared-prod-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.rg-production-shared-prod-001.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/21dc48e9-b2ef-4158-bc7b-7af56a4df1cb/resourceGroups/rg-production-shared-prod-001", "location": "westus2", "managed_by": "", "name": "rg-production-shared-prod-001", "tags": {"environment": "prod"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}], "check_results": null}