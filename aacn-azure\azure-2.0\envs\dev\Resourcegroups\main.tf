module "rg-devtest-connectivity-001" {
  source = "./rg-devtest-connectivity-001"
}

module "rg-devtest-netforum-dev-001" {
  source = "./rg-devtest-netforum-dev-001"
}

module "rg-devtest-aacnorg-dev-001" {
  source = "./rg-devtest-aacnorg-dev-001"
}

module "rg-devtest-IIS-dev-001" {
  source = "./rg-devtest-IIS-dev-001"
}

module "rg-devtest-online-cert-shared-001" {
  source = "./rg-devtest-online-cert-shared-001"
}

module "rg-devtest-assessment-dev-001" {
  source = "./rg-devtest-assessments-dev-001"
}

module "rg-devtest-taxonomy-dev-001" {
  source = "./rg-devtest-taxonomy-dev-001"
}

module "rg-devtest-cert-cequeue-001" {
  source = "./rg-devtest-cert-cequeue-001"
}

module "rg-devtest-cert-certtestcenter-001" {
  source = "./rg-devtest-cert-certtestcenter-001"
}

module "rg-devtest-chapters-nursingnerwork-001" {
  source = "./rg-devtest-chapters-nursingnerwork-001"
}

module "rg-devtest-hwe-hweatteams-001" {
  source = "./rg-devtest-hwe-hweatteams-001"
}

module "rg-devtest-learning-courses-001" {
  source = "./rg-devtest-learning-courses-001"
}

module "rg-devtest-nursing-beaconv3-001" {
  source = "./rg-devtest-nursing-beaconv3-001"
}

module "rg-devtest-nursing-beaconsurvey-001" {
  source = "./rg-devtest-nursing-beaconsurvey-001"
}

# module "rg-devtest-nti-eventsv3-001" {
#   source = "./rg-devtest-nti-eventsv3-001"
# }

module "rg-devtest-nti-programapproval-001" {
  source = "./rg-devtest-nti-programapproval-001"
}

module "rg-devtest-lighthouse-DW-001" {
  source = "./rg-devtest-lighthouse-DW-001"
}

module "rg-devtest-lighthouse-fabric-001" {
  source = "./rg-devtest-lighthouse-fabric-001"
}

module "rg-devtest-lighthouse-taxonomy-001" {
  source = "./rg-devtest-lighthouse-taxonomy-001"
}

module "rg-devetst-mgmt-westus2-001" {
  source = "./rg-devetst-mgmt-westus2-001"
}

module "vm-devtest-testvm-001" {
  source = "./vm-devtest-testvm-001"
}

module "rg-devtest-shared-001" {
  source = "./rg-devtest-shared-001"
}