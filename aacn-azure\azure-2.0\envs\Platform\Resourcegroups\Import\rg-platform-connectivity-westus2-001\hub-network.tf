module "hub_network" {
  source = "../../../../../modules/networking/hub_vnet"
  vnet_name              = "vnet-platform-hub-westus2-001"
  address_space          = ["********/16"]
  location               = var.location
  resource_group_name    = var.resource_group_name
  subnet_name            = "snet-identity-dc"
  subnet_address_prefixes = ["********/24"]
}

resource "azurerm_subnet" "gateway_subnet" {
  name                 = "GatewaySubnet"
  resource_group_name  = var.resource_group_name
  virtual_network_name = "vnet-platform-hub-westus2-001"
  address_prefixes     = ["********/24"]
}
# VPN Gateway 
resource "azurerm_public_ip" "vpn_gateway_ip" {
  name                = "pip-gw-vnet-platform-hub-westus2-001"
  location            = var.location
  resource_group_name = var.resource_group_name
  allocation_method   = "Static"
  sku                  = "Standard"
    # Zone-redundant Public IP (using the zones argument)
  zones = ["1", "2", "3"]
}
resource "azurerm_virtual_network_gateway" "vpn_gateway" {
  name                = "gw-v-vnet-platform-hub-westus2-001"
  location            = var.location
  resource_group_name = var.resource_group_name
  type                = "Vpn"
  vpn_type            = "RouteBased"
  active_active       = false
  enable_bgp          = false
  sku                 = "VpnGw2AZ"
  ip_configuration {
    name                          = "vnet-gateway-config"
    public_ip_address_id          = azurerm_public_ip.vpn_gateway_ip.id
    private_ip_address_allocation = "Dynamic"
    subnet_id                     = azurerm_subnet.gateway_subnet.id
  }
}
resource "azurerm_local_network_gateway" "on_prem" {
  name                = "gw-l-platform-hub-vpn-hq-westus2-001"
  location            = var.location
  resource_group_name = var.resource_group_name
  gateway_address     = "************"
  address_space       = ["*************/24", "**********/22"]
}
# VPN Connection
resource "azurerm_virtual_network_gateway_connection" "vpn_connection" {
  name                           = "cn-gw-l-platform-westus2-001-to-gw-v-production-westus2-001"
  location                       = var.location
  resource_group_name            = var.resource_group_name
  virtual_network_gateway_id     = azurerm_virtual_network_gateway.vpn_gateway.id
  local_network_gateway_id       = azurerm_local_network_gateway.on_prem.id
  type                           = "IPsec" 
  routing_weight                 = 10
  shared_key                     = "8TLg7c6lqiLPiCMDgeK9UWOAmTRwcktX"
}