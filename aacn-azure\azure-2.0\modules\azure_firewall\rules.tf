# # Network Rule Collection - Allow HTTP & HTTPS
# resource "azurerm_firewall_network_rule_collection" "allow_http_https" {
#   name                = "Allow-HTTP-HTTPS"
#   azure_firewall_name = azurerm_firewall.this.name
#   resource_group_name = var.resource_group_name
#   priority            = 100
#   action              = "Allow"

#   rule {
#     name                  = "Allow-HTTP"
#     protocols             = ["TCP"]
#     source_addresses      = ["*"]
#     destination_addresses = ["*"]
#     destination_ports     = ["80"]
#   }

#   rule {
#     name                  = "Allow-HTTPS"
#     protocols             = ["TCP"]
#     source_addresses      = ["*"]
#     destination_addresses = ["*"]
#     destination_ports     = ["443"]
#   }
# }

# # Application Rule Collection - Allow Web Traffic
# resource "azurerm_firewall_application_rule_collection" "allow_web_traffic" {
#   name                = "Allow-Web-Traffic"
#   azure_firewall_name = azurerm_firewall.this.name
#   resource_group_name = var.resource_group_name
#   priority            = 200
#   action              = "Allow"

#   rule {
#     name            = "Allow-Web"
#     source_addresses = ["*"]

#     protocol {
#       type = "Http"
#       port = "80"
#     }

#     protocol {
#       type = "Https"
#       port = "443"
#     }

#     target_fqdns = ["*"]
#   }
# }

# # Default Deny Rule
# resource "azurerm_firewall_network_rule_collection" "deny_all" {
#   name                = "Deny-All-Traffic"
#   azure_firewall_name = azurerm_firewall.this.name
#   resource_group_name = var.resource_group_name
#   priority            = 300
#   action              = "Deny"

#   rule {
#     name                  = "Deny-All"
#     protocols             = ["TCP", "UDP"]
#     source_addresses      = ["*"]
#     destination_addresses = ["*"]
#     destination_ports     = ["*"]
#   }
# }