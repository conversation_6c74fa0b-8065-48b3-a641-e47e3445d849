# set -e

# usage() {
#     echo "Usage: $0 -e ENVIRONMENT -b BASE_PATH [-g RESOURCE_GROUP] COMMAND [ARGS...]"
#     echo "  -e ENVIRONMENT      The environment to target (dev, prod, Platform)"
#     echo "  -b BASE_PATH        The base path to set in the script (relative to the script's directory)"
#     echo "  -g RESOURCE_GROUP   The resource group to target (optional)"
#     echo "  COMMAND             The terraform command to run (plan, apply --auto-approve, destroy --auto-approve, state list, state rm, etc.)"
#     echo "  ARGS                Additional arguments for the terraform command (optional)"
#     echo "Press any key to exit..."
#     read -n 1
#     exit 1
# }

# while getopts ":e:b:g:" opt; do
#     case $opt in
#         e) ENVIRONMENT="$OPTARG"
#         ;;
#         b) BASE_PATH="$OPTARG"
#         ;;
#         g) RESOURCE_GROUP="$OPTARG"
#         ;;
#         \?) usage
#         ;;
#     esac
# done
# shift $((OPTIND -1))

# if [ -z "$ENVIRONMENT" ] || [ -z "$BASE_PATH" ] || [ -z "$1" ]; then
#     usage
# fi

# TERRAFORM_COMMAND="$1"
# shift

# # Set subscription ID based on environment
# case "$ENVIRONMENT" in
#     dev)
#         AZURE_SUBSCRIPTION_ID="067e726d-1a79-4cc3-aba1-7fb1398638b5"
#         ;;
#     Platform)
#         AZURE_SUBSCRIPTION_ID="fce95a24-209c-4e69-98de-885660170e1d"
#         ;;
#     prod)
#         AZURE_SUBSCRIPTION_ID="21dc48e9-b2ef-4158-bc7b-7af56a4df1cb"
#         ;;
#     *)
#         usage
#         echo "Press any key to exit..."
#         read -n 1
#         exit 1
#         ;;
# esac

# # Set the Azure subscription for run in local using User
# # az account set --subscription "$AZURE_SUBSCRIPTION_ID"

# # Set Terraform environment variables for service principal authentication for Azure DevOps Pipeline.
# export ARM_CLIENT_ID="${ARM_CLIENT_ID}"
# export ARM_CLIENT_SECRET="${ARM_CLIENT_SECRET}"
# export ARM_TENANT_ID="${ARM_TENANT_ID}"
# export ARM_SUBSCRIPTION_ID="$AZURE_SUBSCRIPTION_ID"

# # Ensure no other authentication context is interfering
# # az account clear

# SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
# ABS_BASE_PATH="$(cd "$SCRIPT_DIR/$BASE_PATH" && pwd)"

# if [ -n "$RESOURCE_GROUP" ]; then
#     if [ "$ENVIRONMENT" = "Platform" ]; then
#         BASE_DIR="${ABS_BASE_PATH}/${ENVIRONMENT}/Resourcegroups/Import/${RESOURCE_GROUP}"
#     else
#         BASE_DIR="${ABS_BASE_PATH}/${ENVIRONMENT}/Resourcegroups/${RESOURCE_GROUP}"
#     fi
# else
#     BASE_DIR="${ABS_BASE_PATH}/${ENVIRONMENT}/Resourcegroups"
# fi

# echo "SCRIPT_DIR is set to: $SCRIPT_DIR"
# echo "BASE_PATH is set to: $BASE_PATH"
# echo "ABS_BASE_PATH is set to: $ABS_BASE_PATH"
# echo "BASE_DIR is set to: $BASE_DIR"

# echo "ARM_CLIENT_ID: $ARM_CLIENT_ID"
# echo "ARM_CLIENT_SECRET: $ARM_CLIENT_SECRET"
# echo "ARM_TENANT_ID: ${ARM_TENANT_ID}"
# echo "ARM_SUBSCRIPTION_ID: $ARM_SUBSCRIPTION_ID"

# if [ ! -d "$BASE_DIR" ]; then
#     echo "Error: Directory $BASE_DIR does not exist."
#     echo "Press any key to exit..."
#     read -n 1
#     exit 1
# fi

# echo "Processing in directory: $BASE_DIR"
# cd "$BASE_DIR"

# # Delete .terraform directory if it exists
# if [ -d ".terraform" ]; then
#     echo "Deleting .terraform directory to clean up stale state file, modules and plugins so to avoid Azure init errors"
#     rm -rf .terraform
# fi

# echo "Running: terraform init"
# terraform init
# echo "Running: terraform fmt"
# terraform fmt
# echo "Running: terraform validate"
# terraform validate

# case "$TERRAFORM_COMMAND" in
#     state)
#         if [ "$1" = "list" ]; then
#             echo "Running: terraform state list"
#             terraform state list
#         elif [ "$1" = "rm" ]; then
#             shift
#             echo "Running: terraform state rm $@"
#             terraform state rm "$@"
#         else
#             usage
#         fi
#         ;;
#     *)
#         echo "Running: terraform $TERRAFORM_COMMAND $@"
#         terraform $TERRAFORM_COMMAND "$@"
#         ;;
# esac

################## statemv & import ########################

set -e

usage() {
    echo "Usage: $0 -e ENVIRONMENT -b BASE_PATH [-g RESOURCE_GROUP] COMMAND [ARGS...]"
    echo "  -e ENVIRONMENT      The environment to target (dev, prod, Platform)"
    echo "  -b BASE_PATH        The base path to set in the script (relative to the script's directory)"
    echo "  -g RESOURCE_GROUP   The resource group to target (optional)"
    echo "  COMMAND             The terraform command to run (plan, apply --auto-approve, destroy --auto-approve, state list, state rm, state mv, state show, import, show, etc.)"
    echo "  ARGS                Additional arguments for the terraform command (optional)"
    echo "Press any key to exit..."
    read -n 1
    exit 1
}

while getopts ":e:b:g:" opt; do
    case $opt in
        e) ENVIRONMENT="$OPTARG"
        ;;
        b) BASE_PATH="$OPTARG"
        ;;
        g) RESOURCE_GROUP="$OPTARG"
        ;;
        \?) usage
        ;;
    esac
done
shift $((OPTIND -1))

if [ -z "$ENVIRONMENT" ] || [ -z "$BASE_PATH" ] || [ -z "$1" ]; then
    usage
fi

TERRAFORM_COMMAND="$1"
shift

# Set subscription ID based on environment
case "$ENVIRONMENT" in
    dev)
        AZURE_SUBSCRIPTION_ID="067e726d-1a79-4cc3-aba1-7fb1398638b5"
        ;;
    Platform)
        AZURE_SUBSCRIPTION_ID="fce95a24-209c-4e69-98de-885660170e1d"
        ;;
    prod)
        AZURE_SUBSCRIPTION_ID="21dc48e9-b2ef-4158-bc7b-7af56a4df1cb"
        ;;
    *)
        usage
        echo "Press any key to exit..."
        read -n 1
        exit 1
        ;;
esac

# Set the Azure subscription for run in local using User
# az account set --subscription "$AZURE_SUBSCRIPTION_ID"

# Set Terraform environment variables for service principal authentication for Azure DevOps Pipeline.
export ARM_CLIENT_ID="${ARM_CLIENT_ID}"
export ARM_CLIENT_SECRET="${ARM_CLIENT_SECRET}"
export ARM_TENANT_ID="${ARM_TENANT_ID}"
export ARM_SUBSCRIPTION_ID="$AZURE_SUBSCRIPTION_ID"

# Ensure no other authentication context is interfering
# az account clear

SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
ABS_BASE_PATH="$(cd "$SCRIPT_DIR/$BASE_PATH" && pwd)"

if [ -n "$RESOURCE_GROUP" ]; then
    if [ "$ENVIRONMENT" = "Platform" ]; then
        BASE_DIR="${ABS_BASE_PATH}/${ENVIRONMENT}/Resourcegroups/Import/${RESOURCE_GROUP}"
    else
        BASE_DIR="${ABS_BASE_PATH}/${ENVIRONMENT}/Resourcegroups/${RESOURCE_GROUP}"
    fi
else
    BASE_DIR="${ABS_BASE_PATH}/${ENVIRONMENT}/Resourcegroups"
fi

echo "SCRIPT_DIR is set to: $SCRIPT_DIR"
echo "BASE_PATH is set to: $BASE_PATH"
echo "ABS_BASE_PATH is set to: $ABS_BASE_PATH"
echo "BASE_DIR is set to: $BASE_DIR"

echo "ARM_CLIENT_ID: $ARM_CLIENT_ID"
echo "ARM_CLIENT_SECRET: $ARM_CLIENT_SECRET"
echo "ARM_TENANT_ID: ${ARM_TENANT_ID}"
echo "ARM_SUBSCRIPTION_ID: $ARM_SUBSCRIPTION_ID"

if [ ! -d "$BASE_DIR" ]; then
    echo "Error: Directory $BASE_DIR does not exist."
    echo "Press any key to exit..."
    read -n 1
    exit 1
fi

echo "Processing in directory: $BASE_DIR"
cd "$BASE_DIR"

# Delete .terraform directory if it exists
if [ -d ".terraform" ]; then
    echo "Deleting .terraform directory to clean up stale state file, modules and plugins so to avoid Azure init errors"
    rm -rf .terraform
fi

echo "Running: terraform init"
terraform init
echo "Running: terraform fmt"
terraform fmt
echo "Running: terraform validate"
terraform validate

case "$TERRAFORM_COMMAND" in
    state)
        if [ "$1" = "list" ]; then
            echo "Running: terraform state list"
            terraform state list
        elif [ "$1" = "rm" ]; then
            shift
            echo "Running: terraform state rm $@"
            terraform state rm "$@"
        elif [ "$1" = "mv" ]; then
            shift
            echo "Running: terraform state mv $@"
            terraform state mv "$@"
        elif [ "$1" = "show" ]; then    
            shift
            echo "Running: terraform state show $@"
            terraform state show "$@"
        else
            usage
        fi
        ;;
    import)
        echo "Running: terraform import $@"
        terraform import "$@"
        ;;    
    *)
        echo "Running: terraform $TERRAFORM_COMMAND $@"
        terraform $TERRAFORM_COMMAND "$@"
        ;;
esac