module "key_vaults" {
  source              = "../../../../modules/key_vaults"  
  resource_group_name = var.resource_group_name
  location            = var.location
  key_vaults_tags     = var.key_vaults_tags
  tenant_id           = var.tenant_id
  sku                 = var.sku                 
    
  key_vault_names = {
    for name in var.key_vault_names : name => name
  }

  access_policies = flatten([
    for key_vault_name in var.key_vault_names : [
      for object_id in var.access_policy_object_ids : {
        key_vault_name            = key_vault_name
        tenant_id                 = var.tenant_id
        object_id                 = object_id
        secret_permissions        = var.secret_permissions
        key_permissions           = var.key_permissions
        certificate_permissions   = var.certificate_permissions
      }
    ]
  ])
}

