# Log Analytics Workspace
# resource "azurerm_log_analytics_workspace" "log_analytics" {
#   name                = var.log_analytics_workspace_name
#   location            = var.location
#   resource_group_name = var.resource_group_name
#   sku                 = var.log_analytics_workspace_sku   # Pay-As-You-Go model
#   retention_in_days   = var.log_analytics_retention_in_days # Adjust retention as needed
# }

# Microsoft Sentinel
# resource "azurerm_sentinel_log_analytics_workspace_onboarding" "sentinel" {
#   workspace_id = azurerm_log_analytics_workspace.log_analytics.id
# }

# Srorage accout 

# module "sentinel_storage" {
#   source               = "../../../../../modules/storage_account"
#   storage_account_name = var.sentinel_storage_account_name  
#   resource_group_name  = var.resource_group_name
#   location             = var.location
#   tags                 = var.resource_group_tags
#   container_name       = "snetlogs"
# }

# # Link Storage Account to Log Analytics Workspace for specific data types
# resource "azurerm_monitor_diagnostic_setting" "log_analytics_diagnostic" {
#   name               = "${azurerm_log_analytics_workspace.log_analytics.name}-diag"
#   target_resource_id = azurerm_log_analytics_workspace.log_analytics.id
#   storage_account_id = module.sentinel_storage.storage_account_id

# }

# # Link Storage Account to Log Analytics Workspace for specific data types
# resource "azurerm_log_analytics_workspace_linked_storage_account" "linked_storage" {
#   log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics.id
#   storage_account_ids        = [var.existing_storage_account_id]

#   # Specify the types of data to store in the linked storage account
#   data_types = [
#     "CustomLogs",     # For custom logs
#     "AzureWatson",    # For saved and log alert queries
#     "QueryLogs"       # For queries in the Log Analytics workspace
#   ]
# }