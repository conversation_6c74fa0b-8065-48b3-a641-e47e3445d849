module "spoke1_vnet" {
  source = "../../../../modules/networking/spoke_vnet"

  vnet_name              = "vnet-westus2-corp-prod"
  address_space          = ["********/16"]
  location               = var.location
  resource_group_name    = var.resource_group_name
  subnet_name            = "snet-corp-PRODUCT-prod"
  subnet_address_prefixes = ["********/24"]

    providers = {
    azurerm = azurerm.production
  }
}

#Outputs
output "spoke1_vnet_name" {
  value = module.spoke1_vnet.vnet_name
}

output "spoke1_vnet_id" {
  value = module.spoke1_vnet.vnet_id
}

output "spoke1_subnet_id" {
  value = module.spoke1_vnet.subnet_id
}