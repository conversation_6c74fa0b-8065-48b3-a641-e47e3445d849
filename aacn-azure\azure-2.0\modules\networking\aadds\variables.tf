variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "location" {
  description = "The Azure region where resources will be created"
  type        = string
}

variable "name_prefix" {
  description = "Prefix for resource names"
  type        = string
  default     = "LK-JWW7L0ZFMZB4"
}

variable "public_ip_name" {
  description = "Name of the public IP address"
  type        = string
}

variable "domain_name_label" {
  description = "Domain name label for the public IP"
  type        = string
  default     = null
}

variable "lb_name" {
  description = "Name of the load balancer"
  type        = string
}

variable "subnet_id" {
  description = "ID of the subnet where NICs will be created"
  type        = string
}

variable "nic_name_1" {
  description = "Name of the first network interface"
  type        = string
}

variable "nic_name_2" {
  description = "Name of the second network interface"
  type        = string
}

variable "dns_servers" {
  description = "List of DNS servers for the network interfaces"
  type        = list(string)
  default     = []
}

variable "nat_rule_prefix_1" {
  description = "Prefix for the first NAT rule"
  type        = string
  default     = "GPXUMNTT4QWKXBVP"
}

variable "nat_rule_prefix_2" {
  description = "Prefix for the second NAT rule"
  type        = string
  default     = "W-93GOMVYO3AONG"
}

variable "ip_config_prefix_1" {
  description = "Prefix for the first IP configuration"
  type        = string
  default     = "GPXUMNTT4QWKXBV"
}

variable "ip_config_prefix_2" {
  description = "Prefix for the second IP configuration"
  type        = string
  default     = "W-93GOMVYO3AONG"
}

variable "frontend_port_1" {
  description = "Frontend port for the first NAT rule"
  type        = number
  default     = 5986
}

variable "frontend_port_2" {
  description = "Frontend port for the second NAT rule"
  type        = number
  default     = 5987
}

variable "backend_port" {
  description = "Backend port for NAT rules"
  type        = number
  default     = 5986
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}