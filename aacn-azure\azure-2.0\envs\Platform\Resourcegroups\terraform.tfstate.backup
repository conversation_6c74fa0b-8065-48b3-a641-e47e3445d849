{"version": 4, "terraform_version": "1.7.5", "serial": 15, "lineage": "c16bca5a-aaa5-abc5-0e28-f873c84e31f8", "outputs": {}, "resources": [{"module": "module.cloudpc.module.rg-platform-cloudpc-westus2-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.cloudpc.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-cloudpc-westus2-001", "location": "westus2", "managed_by": "", "name": "rg-platform-cloudpc-westus2-001", "tags": {"CostCenter": "1911-51210", "environment": "Platform"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.comms.module.rg-platform-comms-westus2-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.comms.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-comms-westus2-001", "location": "westus2", "managed_by": "", "name": "rg-platform-comms-westus2-001", "tags": {"CostCenter": "1911-51210", "environment": "Platform"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.connectivity.module.rg-platform-connectivity-westus2-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.connectivity.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001", "location": "westus2", "managed_by": "", "name": "rg-platform-connectivity-westus2-001", "tags": {"CostCenter": "1911-51210", "environment": "Platform"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.devops.module.rg-platform-devops-centralus-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.devops.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-devops-centralus-001", "location": "westus2", "managed_by": "", "name": "rg-platform-devops-centralus-001", "tags": {"CostCenter": "1911-51210", "environment": "Platform"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.honeypot.module.rg-platform-honeypot-westus2-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.honeypot.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-honeypot-westus2-001", "location": "westus2", "managed_by": "", "name": "rg-platform-honeypot-westus2-001", "tags": {"CostCenter": "1911-51210", "environment": "Platform"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.identity.module.rg-platform-identity-westus2-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.identity.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-identity-westus2-001", "location": "westus2", "managed_by": "", "name": "rg-platform-identity-westus2-001", "tags": {"CostCenter": "1911-51210", "Environment": "Development"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.keyvaultmanager.module.rg-platform-keyvaultmanager-westus2-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.keyvaultmanager.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-keyvaultmanager-westus2-001", "location": "westus2", "managed_by": "", "name": "rg-platform-keyvaultmanager-westus2-001", "tags": {"CostCenter": "1911-51210", "environment": "Platform"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.keyvaultmanager.module.stkeyvaultmanager001", "mode": "managed", "type": "azurerm_key_vault", "name": "key_vault", "provider": "module.keyvaultmanager.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 2, "attributes": {"access_policy": [], "contact": [], "enable_rbac_authorization": false, "enabled_for_deployment": false, "enabled_for_disk_encryption": false, "enabled_for_template_deployment": false, "id": "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-keyvaultmanager-westus2-001/providers/Microsoft.KeyVault/vaults/stkeyvaultmanager001", "location": "westus2", "name": "stkeyvaultmanager001", "network_acls": [{"bypass": "AzureServices", "default_action": "Allow", "ip_rules": [], "virtual_network_subnet_ids": []}], "public_network_access_enabled": true, "purge_protection_enabled": false, "resource_group_name": "rg-platform-keyvaultmanager-westus2-001", "sku_name": "standard", "soft_delete_retention_days": 90, "tags": {"environment": "Platform"}, "tenant_id": "ca4fd2d7-85ea-42d2-b7b3-30ef2666c7ab", "timeouts": null, "vault_uri": "https://stkeyvaultmanager001.vault.azure.net/"}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************************************************************************"}]}, {"module": "module.mgmt.module.platform_storage", "mode": "managed", "type": "azurerm_storage_account", "name": "storage", "provider": "module.mgmt.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 4, "attributes": {"access_tier": "Hot", "account_kind": "StorageV2", "account_replication_type": "LRS", "account_tier": "Standard", "allow_nested_items_to_be_public": true, "allowed_copy_scope": "", "azure_files_authentication": [], "blob_properties": [{"change_feed_enabled": false, "change_feed_retention_in_days": 0, "container_delete_retention_policy": [], "cors_rule": [], "default_service_version": "", "delete_retention_policy": [], "last_access_time_enabled": false, "restore_policy": [], "versioning_enabled": false}], "cross_tenant_replication_enabled": true, "custom_domain": [], "customer_managed_key": [], "default_to_oauth_authentication": false, "dns_endpoint_type": "Standard", "edge_zone": "", "enable_https_traffic_only": true, "id": "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-mgmt-westus2-001/providers/Microsoft.Storage/storageAccounts/stplatformterra001", "identity": [], "immutability_policy": [], "infrastructure_encryption_enabled": false, "is_hns_enabled": false, "large_file_share_enabled": null, "local_user_enabled": true, "location": "westus2", "min_tls_version": "TLS1_2", "name": "stplatformterra001", "network_rules": [{"bypass": ["AzureServices"], "default_action": "Allow", "ip_rules": [], "private_link_access": [], "virtual_network_subnet_ids": []}], "nfsv3_enabled": false, "primary_access_key": "****************************************************************************************", "primary_blob_connection_string": "DefaultEndpointsProtocol=https;BlobEndpoint=https://stplatformterra001.blob.core.windows.net/;AccountName=stplatformterra001;AccountKey=****************************************************************************************", "primary_blob_endpoint": "https://stplatformterra001.blob.core.windows.net/", "primary_blob_host": "stplatformterra001.blob.core.windows.net", "primary_blob_internet_endpoint": "", "primary_blob_internet_host": "", "primary_blob_microsoft_endpoint": "", "primary_blob_microsoft_host": "", "primary_connection_string": "DefaultEndpointsProtocol=https;AccountName=stplatformterra001;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "primary_dfs_endpoint": "https://stplatformterra001.dfs.core.windows.net/", "primary_dfs_host": "stplatformterra001.dfs.core.windows.net", "primary_dfs_internet_endpoint": "", "primary_dfs_internet_host": "", "primary_dfs_microsoft_endpoint": "", "primary_dfs_microsoft_host": "", "primary_file_endpoint": "https://stplatformterra001.file.core.windows.net/", "primary_file_host": "stplatformterra001.file.core.windows.net", "primary_file_internet_endpoint": "", "primary_file_internet_host": "", "primary_file_microsoft_endpoint": "", "primary_file_microsoft_host": "", "primary_location": "westus2", "primary_queue_endpoint": "https://stplatformterra001.queue.core.windows.net/", "primary_queue_host": "stplatformterra001.queue.core.windows.net", "primary_queue_microsoft_endpoint": "", "primary_queue_microsoft_host": "", "primary_table_endpoint": "https://stplatformterra001.table.core.windows.net/", "primary_table_host": "stplatformterra001.table.core.windows.net", "primary_table_microsoft_endpoint": "", "primary_table_microsoft_host": "", "primary_web_endpoint": "https://stplatformterra001.z5.web.core.windows.net/", "primary_web_host": "stplatformterra001.z5.web.core.windows.net", "primary_web_internet_endpoint": "", "primary_web_internet_host": "", "primary_web_microsoft_endpoint": "", "primary_web_microsoft_host": "", "public_network_access_enabled": true, "queue_encryption_key_type": "Service", "queue_properties": [{"cors_rule": [], "hour_metrics": [{"enabled": true, "include_apis": true, "retention_policy_days": 7, "version": "1.0"}], "logging": [{"delete": false, "read": false, "retention_policy_days": 0, "version": "1.0", "write": false}], "minute_metrics": [{"enabled": false, "include_apis": false, "retention_policy_days": 0, "version": "1.0"}]}], "resource_group_name": "rg-platform-mgmt-westus2-001", "routing": [], "sas_policy": [], "secondary_access_key": "****************************************************************************************", "secondary_blob_connection_string": "", "secondary_blob_endpoint": null, "secondary_blob_host": null, "secondary_blob_internet_endpoint": null, "secondary_blob_internet_host": null, "secondary_blob_microsoft_endpoint": null, "secondary_blob_microsoft_host": null, "secondary_connection_string": "DefaultEndpointsProtocol=https;AccountName=stplatformterra001;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "secondary_dfs_endpoint": null, "secondary_dfs_host": null, "secondary_dfs_internet_endpoint": null, "secondary_dfs_internet_host": null, "secondary_dfs_microsoft_endpoint": null, "secondary_dfs_microsoft_host": null, "secondary_file_endpoint": null, "secondary_file_host": null, "secondary_file_internet_endpoint": null, "secondary_file_internet_host": null, "secondary_file_microsoft_endpoint": null, "secondary_file_microsoft_host": null, "secondary_location": "", "secondary_queue_endpoint": null, "secondary_queue_host": null, "secondary_queue_microsoft_endpoint": null, "secondary_queue_microsoft_host": null, "secondary_table_endpoint": null, "secondary_table_host": null, "secondary_table_microsoft_endpoint": null, "secondary_table_microsoft_host": null, "secondary_web_endpoint": null, "secondary_web_host": null, "secondary_web_internet_endpoint": null, "secondary_web_internet_host": null, "secondary_web_microsoft_endpoint": null, "secondary_web_microsoft_host": null, "sftp_enabled": false, "share_properties": [{"cors_rule": [], "retention_policy": [{"days": 7}], "smb": []}], "shared_access_key_enabled": true, "static_website": [], "table_encryption_key_type": "Service", "tags": {"CostCenter": "1911-51210", "environment": "Platform"}, "timeouts": null}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************************************************************************"}]}, {"module": "module.mgmt.module.platform_storage", "mode": "managed", "type": "azurerm_storage_container", "name": "tfstate", "provider": "module.mgmt.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 1, "attributes": {"container_access_type": "private", "default_encryption_scope": "$account-encryption-key", "encryption_scope_override_enabled": true, "has_immutability_policy": false, "has_legal_hold": false, "id": "https://stplatformterra001.blob.core.windows.net/tfstate", "metadata": {}, "name": "tfstate", "resource_manager_id": "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-mgmt-westus2-001/providers/Microsoft.Storage/storageAccounts/stplatformterra001/blobServices/default/containers/tfstate", "storage_account_name": "stplatformterra001", "timeouts": null}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["module.mgmt.module.platform_storage.azurerm_storage_account.storage"]}]}, {"module": "module.mgmt.module.rg-platform-mgmt-westus2-001", "mode": "managed", "type": "azurerm_resource_group", "name": "rg", "provider": "module.mgmt.provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-mgmt-westus2-001", "location": "westus2", "managed_by": "", "name": "rg-platform-mgmt-westus2-001", "tags": {"CostCenter": "1911-51210", "environment": "Platform"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}], "check_results": null}