variable "frontdoor_name" {
  description = "Name of the Azure Front Door instance"
  type        = string
}

variable "resource_group_name" {
  description = "Name of the resource group"
  type        = string
}

variable "accepted_protocols" {
  description = "Accepted protocols for the Front Door"
  type        = list(string)
}

variable "patterns_to_match" {
  description = "Patterns to match for the routing rule"
  type        = list(string)
}

variable "frontend_endpoint_name" {
  description = "Name of the frontend endpoint"
  type        = string
}

variable "frontend_host_name" {
  description = "Host name of the frontend endpoint"
  type        = string
}

variable "backend_pool_name" {
  description = "Name of the backend pool"
  type        = string
}

variable "backend_host" {
  description = "Host header for the backend"
  type        = string
}

variable "backend_address" {
  description = "Backend address"
  type        = string
}

variable "backend_http_port" {
  description = "HTTP port for the backend"
  type        = number
}

variable "backend_https_port" {
  description = "HTTPS port for the backend"
  type        = number
}

variable "load_balancing_name" {
  description = "Name of the load balancing settings"
  type        = string
}

variable "health_probe_name" {
  description = "Name of the health probe settings"
  type        = string
}