variable "resource_group_name" {
  default = "rg-platform-connectivity-westus2-001"
}

variable "location" {
  default = "West US 2"
}

variable "subscription_id" {
  default = "fce95a24-209c-4e69-98de-885660170e1d"
}
variable "resource_group_tags" {
  type = map(string)
  default = {
    environment = "Platform"
    CostCenter  = "1911-51210"
  }
}

################Firewall#########################
# variable "firewall_name" {
#   default = "afw-platform-hub-westus2-01"
# }

# variable "sku" {
#   default = "AZFW_VNet"
# }

# variable "sku_tier" {
#   default = "Standard"
# }

# variable "firewall_subnet_name" {
#   default = "AzureFirewallSubnet"
# }

########################### Front_door #######################################

variable "frontdoor_name" {
  description = "Name of the Azure Front Door instance"
  default = "afd-platform-hub-westus2-001"
  type        = string
}

variable "accepted_protocols" {
  description = "Accepted protocols for the Front Door"
  type        = list(string)
  default     = ["Http", "Https"]
}

variable "patterns_to_match" {
  description = "Patterns to match for the routing rule"
  type        = list(string)
  default     = ["/*"]
}

variable "frontend_endpoint_name" {
  description = "Name of the frontend endpoint"
  type        = string
  default = "afdend-wwwaacnorg-dev"
}

variable "frontend_host_name" {
  description = "Host name of the frontend endpoint"
  type        = string
  default = "wwwaacnorg-dev"
}

variable "backend_pool_name" {
  description = "Name of the backend pool"
  type        = string
  default = "afdpool-wwwaacnorg-dev"
}

variable "backend_host" {
  description = "Host header for the backend"
  type        = string
  default = "web-chaptersportal-dev-001.azurewebsites.net"
}

variable "backend_address" {
  description = "Backend address"
  type        = string
  default = "web-chaptersportal-dev-001.azurewebsites.net"
}

variable "backend_http_port" {
  description = "HTTP port for the backend"
  type        = number
  default     = 80
}

variable "backend_https_port" {
  description = "HTTPS port for the backend"
  type        = number
  default     = 443
}

variable "load_balancing_name" {
  description = "Name of the load balancing settings"
  type        = string
  default = "afdload-wwwaacnorg-dev"
}

variable "health_probe_name" {
  description = "Name of the health probe settings"
  type        = string
  default = "afdhealth-wwwaacnorg-dev"
}
