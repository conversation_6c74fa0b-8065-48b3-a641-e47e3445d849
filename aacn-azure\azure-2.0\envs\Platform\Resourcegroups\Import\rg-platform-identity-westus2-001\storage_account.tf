# resource "azurerm_storage_account" "res-4" {
#   account_replication_type = "LRS"
#   account_tier             = "Standard"
#   location                 = "westus2"
#   name                     = "stmgmtdev001"
#   resource_group_name      = "rg-westus2-mgmt-dev-001"
#   tags = {
#     CostCenter  = "1911-51210"
#     Environment = "Development"
#   }
#   depends_on = [
#     module.rg_westus2_mgmt_dev_001,
#   ]
# }
# resource "azurerm_storage_container" "res-6" {
#   container_access_type = "container"
#   name                  = "keyvaultartifact"
#   storage_account_name  = "stmgmtdev001"
# }