
# # Set available environments and modules
# environments=("dev" "prod")
# modules=("mgmt_groups" "network" "compute" "storage" "subscriptions")

# # Function to select environment
# select_environment() {
#   echo "Select environment:"
#   select env in "${environments[@]}"; do
#     if [[ " ${environments[*]} " == *" $env "* ]]; then
#       echo "Selected environment: $env"
#       break
#     else
#       echo "Invalid selection. Try again."
#     fi
#   done
# }

# # Function to select module
# select_module() {
#   echo "Select module:"
#   select mod in "${modules[@]}"; do
#     if [[ " ${modules[*]} " == *" $mod "* ]]; then
#       echo "Selected module: $mod"
#       break
#     else
#       echo "Invalid selection. Try again."
#     fi
#   done
# }

# # Function to run Terraform
# run_terraform() {
#   local component_path="$1"
#   echo "Running terraform action for $mod in $env environment..."

#   cd "$component_path" || { echo "Failed to change directory to $component_path"; exit 1; }
#   terraform init
#   terraform $2 -target=module.$mod
#   cd - || exit
# }

# # Main script logic
# echo "Welcome to the Terraform runner script!"

# # Check if base directory path argument is provided
# if [ $# -ne 1 ]; then
#   echo "Usage: $0 <base_directory_path>"
#   exit 1
# fi

# base_dir="$1"

# # Validate base directory
# if [ ! -d "$base_dir" ]; then
#   echo "Directory '$base_dir' does not exist or is not accessible."
#   exit 1
# fi

# # Navigate to script directory
# SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
# cd "$SCRIPT_DIR" || exit

# # Select environment and module
# select_environment
# select_module

# # Prompt for action
# echo "What would you like to do?"
# options=("plan" "apply" "destroy" "exit")
# select opt in "${options[@]}"; do
#   case $opt in
#     "plan")
#       run_terraform "$base_dir/envs/$env" "plan"
#       break
#       ;;
#     "apply")
#       run_terraform "$base_dir/envs/$env" "apply"
#       break
#       ;;
#     "destroy")
#       run_terraform "$base_dir/envs/$env" "destroy"
#       break
#       ;;
#     "exit")
#       echo "Exiting script."
#       exit 0
#       ;;
#     *)
#       echo "Invalid option. Try again."
#       ;;
#   esac
# done


############################################################################################################
# # Set available environments and modules
# environments=("dev" "prod")
# modules=("mgmt_groups" "network" "compute" "storage" "subscriptions")

# # Function to run Terraform
# run_terraform() {
#   local base_dir="$1"
#   local env="$2"
#   local mod="$3"
#   local action="$4"

#   local component_path="$base_dir/envs/$env"

#   echo "Running terraform $action for $mod in $env environment..."

#   cd "$component_path" || { echo "Failed to change directory to $component_path"; exit 1; }
#   terraform init
#   terraform $action -target=module.$mod
#   cd - || exit
# }

# # Main script logic
# echo "Welcome to the Terraform runner script!"

# # Check if all required arguments are provided
# if [ $# -ne 4 ]; then
#   echo "Usage: $0 <base_directory_path> <environment> <module> <action>"
#   exit 1
# fi

# base_dir="$1"
# env="$2"
# mod="$3"
# action="$4"

# # Validate environment and module selections
# if [[ ! " ${environments[@]} " =~ " $env " ]]; then
#   echo "Invalid environment. Available environments: ${environments[@]}"
#   exit 1
# fi

# if [[ ! " ${modules[@]} " =~ " $mod " ]]; then
#   echo "Invalid module. Available modules: ${modules[@]}"
#   exit 1
# fi

# # Run Terraform command
# run_terraform "$base_dir" "$env" "$mod" "$action"

#################################################################################################



# set -e

# usage() {
#     echo "Usage: $0 env=ENVIRONMENT resource_group=RESOURCE_GROUP terraform_command=COMMAND"
#     echo "  env               The environment to target (dev, prod, Platform)"
#     # echo "  subscription      The subscription to target (Platform, landingzone, sandbox)"
#     echo "  resource_group    The resource group to target (rg-westus2-mgmt-dev-001, rg-westus2-mgmt-prod-001, rg-westus2-network-iland-001, rg-westus2-network-prod-001)"
#     echo "  terraform_command The terraform command to run (plan, apply, etc.)"
#     exit 1
# }

# for arg in "$@"
# do
#     case $arg in
#         env=*)
#         ENVIRONMENT="${arg#*=}"
#         shift
#         # ;;
#         # subscription=*)
#         # SUBSCRIPTION="${arg#*=}"
#         # shift
#         ;;
#         rg=*)
#         RESOURCE_GROUP="${arg#*=}"
#         shift
#         ;;
#         terraform_command=*)
#         TERRAFORM_COMMAND="${arg#*=}"
#         shift
#         ;;
#         *)
#         usage
#         ;;
#     esac
# done

# if [ -z "$ENVIRONMENT" ] || [ -z "$RESOURCE_GROUP" ] || [ -z "$TERRAFORM_COMMAND" ]; then
#     usage
# fi

# SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
# BASE_DIR="${SCRIPT_DIR}/../envs/${ENVIRONMENT}/Resourcegroups/Import/${RESOURCE_GROUP}"

# echo "SCRIPT_DIR is set to: $SCRIPT_DIR"
# echo "BASE_DIR is set to: $BASE_DIR"

# if [ ! -d "$BASE_DIR" ]; then
#     echo "Error: Directory $BASE_DIR does not exist."
#     exit 1
# fi

# echo "Processing resource group in directory: $BASE_DIR"
# cd "$BASE_DIR"

# terraform init
# terraform fmt
# terraform validate
# terraform $TERRAFORM_COMMAND

##########################################Working#######################################################

# set -e

# usage() {
#     echo "Usage: $0 -e ENVIRONMENT -b BASE_PATH [-g RESOURCE_GROUP] COMMAND"
#     echo "  -e ENVIRONMENT      The environment to target (dev, prod, Platform)"
#     echo "  -b BASE_PATH        The base path to set in the script"
#     echo "  -g RESOURCE_GROUP   The resource group to target (optional)"
#     echo "  COMMAND             The terraform command to run (plan, apply, destroy, etc.)"
#     exit 1
# }

# while getopts ":e:b:g:" opt; do
#     case $opt in
#         e) ENVIRONMENT="$OPTARG"
#         ;;
#         b) BASE_PATH="$OPTARG"
#         ;;
#         g) RESOURCE_GROUP="$OPTARG"
#         ;;
#         \?) usage
#         ;;
#     esac
# done
# shift $((OPTIND -1))

# if [ -z "$ENVIRONMENT" ] || [ -z "$BASE_PATH" ] || [ -z "$1" ]; then
#     usage
# fi

# TERRAFORM_COMMAND="$1"

# SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
# if [ -n "$RESOURCE_GROUP" ]; then
#     BASE_DIR="${BASE_PATH}/${ENVIRONMENT}/Resourcegroups/Import/${RESOURCE_GROUP}"
# else
#     BASE_DIR="${BASE_PATH}/${ENVIRONMENT}"
# fi

# echo "SCRIPT_DIR is set to: $SCRIPT_DIR"
# echo "BASE_DIR is set to: $BASE_DIR"

# if [ ! -d "$BASE_DIR" ]; then
#     echo "Error: Directory $BASE_DIR does not exist."
#     exit 1
# fi

# echo "Processing in directory: $BASE_DIR"
# cd "$BASE_DIR"

# terraform init
# terraform fmt
# terraform validate
# terraform $TERRAFORM_COMMAND

# with rg = ./scripts/run.sh -e Platform -b envs/ -g rg-westus2-network-prod-001 plan
# without rg = ./scripts/run.sh -e dev -b envs/ plan

################################################Working_Latest###################################################

#!/usr/bin/env bash
# set -e

# usage() {
#     echo "Usage: $0 -e ENVIRONMENT -b BASE_PATH [-g RESOURCE_GROUP] COMMAND"
#     echo "  -e ENVIRONMENT      The environment to target (dev, prod, Platform)"
#     echo "  -b BASE_PATH        The base path to set in the script (relative to the script's directory)"
#     echo "  -g RESOURCE_GROUP   The resource group to target (optional)"
#     echo "  COMMAND             The terraform command to run (plan, apply, destroy, etc.)"
#     exit 1
# }

# while getopts ":e:b:g:" opt; do
#     case $opt in
#         e) ENVIRONMENT="$OPTARG"
#         ;;
#         b) BASE_PATH="$OPTARG"
#         ;;
#         g) RESOURCE_GROUP="$OPTARG"
#         ;;
#         \?) usage
#         ;;
#     esac
# done
# shift $((OPTIND -1))

# if [ -z "$ENVIRONMENT" ] || [ -z "$BASE_PATH" ] || [ -z "$1" ]; then
#     usage
# fi

# TERRAFORM_COMMAND="$1"

# SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
# ABS_BASE_PATH="$(cd "$SCRIPT_DIR/$BASE_PATH" && pwd)"

# if [ -n "$RESOURCE_GROUP" ]; then
#     if [ "$ENVIRONMENT" = "Platform" ]; then
#         BASE_DIR="${ABS_BASE_PATH}/${ENVIRONMENT}/Resourcegroups/Import/${RESOURCE_GROUP}"
#     else
#         BASE_DIR="${ABS_BASE_PATH}/${ENVIRONMENT}/Resourcegroups/${RESOURCE_GROUP}"
#     fi
# else
#     BASE_DIR="${ABS_BASE_PATH}/${ENVIRONMENT}/Resourcegroups"
# fi

# echo "SCRIPT_DIR is set to: $SCRIPT_DIR"
# echo "BASE_PATH is set to: $BASE_PATH"
# echo "ABS_BASE_PATH is set to: $ABS_BASE_PATH"
# echo "BASE_DIR is set to: $BASE_DIR"

# if [ ! -d "$BASE_DIR" ]; then
#     echo "Error: Directory $BASE_DIR does not exist."
#     exit 1
# fi

# echo "Processing in directory: $BASE_DIR"
# cd "$BASE_DIR"

# terraform init
# terraform fmt
# terraform validate
# terraform $TERRAFORM_COMMAND

# with rg = ./run.sh -e Platform -b ../envs -g rg-platform-connectivity-westus2-001 plan
# without rg = ./run.sh -e Platform -b ../envs plan

#####07-08-2024###

set -e

usage() {
    echo "Usage: $0 -e ENVIRONMENT -b BASE_PATH [-g RESOURCE_GROUP] COMMAND"
    echo "  -e ENVIRONMENT      The environment to target (dev, prod, Platform)"
    echo "  -b BASE_PATH        The base path to set in the script (relative to the script's directory)"
    echo "  -g RESOURCE_GROUP   The resource group to target (optional)"
    echo "  COMMAND             The terraform command to run (plan, apply --auto-approve, destroy --auto-approve, etc.)"
    echo "Press any key to exit..."
    read -n 1
    exit 1
}

while getopts ":e:b:g:" opt; do
    case $opt in
        e) ENVIRONMENT="$OPTARG"
        ;;
        b) BASE_PATH="$OPTARG"
        ;;
        g) RESOURCE_GROUP="$OPTARG"
        ;;
        \?) usage
        ;;
    esac
done
shift $((OPTIND -1))

if [ -z "$ENVIRONMENT" ] || [ -z "$BASE_PATH" ] || [ -z "$1" ]; then
    usage
fi

TERRAFORM_COMMAND="$1"

# Set subscription ID based on environment
case "$ENVIRONMENT" in
    dev)
        AZURE_SUBSCRIPTION_ID="067e726d-1a79-4cc3-aba1-7fb1398638b5"
        ;;
    Platform)
        AZURE_SUBSCRIPTION_ID="fce95a24-209c-4e69-98de-885660170e1d"
        ;;
    prod)
        AZURE_SUBSCRIPTION_ID="21dc48e9-b2ef-4158-bc7b-7af56a4df1cb"
        ;;
    *)
        usage
        echo "Press any key to exit..."
        read -n 1
        exit 1
        ;;
esac

# Set the Azure subscription for run in local using User
# az account set --subscription "$AZURE_SUBSCRIPTION_ID"


# Set Terraform environment variables for service principal authentication for Azure DevOps Pipeline.

export ARM_CLIENT_ID="${ARM_CLIENT_ID}"

export ARM_CLIENT_SECRET="${ARM_CLIENT_SECRET}"

export ARM_TENANT_ID="${ARM_TENANT_ID}"

export ARM_SUBSCRIPTION_ID="$AZURE_SUBSCRIPTION_ID"

# Ensure no other authentication context is interfering
# az account clear

SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
ABS_BASE_PATH="$(cd "$SCRIPT_DIR/$BASE_PATH" && pwd)"

if [ -n "$RESOURCE_GROUP" ]; then
    if [ "$ENVIRONMENT" = "Platform" ]; then
        BASE_DIR="${ABS_BASE_PATH}/${ENVIRONMENT}/Resourcegroups/Import/${RESOURCE_GROUP}"
    else
        BASE_DIR="${ABS_BASE_PATH}/${ENVIRONMENT}/Resourcegroups/${RESOURCE_GROUP}"
    fi
else
    BASE_DIR="${ABS_BASE_PATH}/${ENVIRONMENT}/Resourcegroups"
fi

echo "SCRIPT_DIR is set to: $SCRIPT_DIR"
echo "BASE_PATH is set to: $BASE_PATH"
echo "ABS_BASE_PATH is set to: $ABS_BASE_PATH"
echo "BASE_DIR is set to: $BASE_DIR"

echo "ARM_CLIENT_ID: $ARM_CLIENT_ID"
echo "ARM_CLIENT_SECRET: $ARM_CLIENT_SECRET"
echo "ARM_TENANT_ID: ${ARM_TENANT_ID}"
echo "ARM_SUBSCRIPTION_ID: $ARM_SUBSCRIPTION_ID"


if [ ! -d "$BASE_DIR" ]; then
    echo "Error: Directory $BASE_DIR does not exist."
    echo "Press any key to exit..."
    read -n 1
    exit 1
fi

echo "Processing in directory: $BASE_DIR"
cd "$BASE_DIR"

# Delete .terraform directory if it exists

if [ -d ".terraform" ]; then
    echo "Deleting .terraform directory to clean up stale state file, modules and plugins so to avoid Azure init errors"
    rm -rf .terraform
fi

echo "Running: terraform init"
terraform init
echo "Running: terraform fmt"
terraform fmt
echo "Running: terraform validate"
terraform validate
echo "Running: terraform $TERRAFORM_COMMAND"
terraform $TERRAFORM_COMMAND
# terraform $TERRAFORM_COMMAND > output.log 2> errors.log
# echo "Press any key to continue..."
# read -n 1
# with rg = ./run.sh -e Platform -b ../envs -g rg-platform-connectivity-westus2-001 plan
# without rg = ./run.sh -e Platform -b ../envs plan
