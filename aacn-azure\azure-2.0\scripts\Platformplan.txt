SCRIPT_DIR is set to: /mnt/c/Users/<USER>/OneDrive - DynaTech Systems Pvt. Ltd/Desktop/Azure-Landing-Zone/Azure2.0/AACN/aacn-azure/azure-2.0/scripts
BASE_PATH is set to: ../envs
ABS_BASE_PATH is set to: /mnt/c/Users/<USER>/OneDrive - DynaTech Systems Pvt. Ltd/Desktop/Azure-Landing-Zone/Azure2.0/AACN/aacn-azure/azure-2.0/envs
BASE_DIR is set to: /mnt/c/Users/<USER>/OneDrive - DynaTech Systems Pvt. Ltd/Desktop/Azure-Landing-Zone/Azure2.0/AACN/aacn-azure/azure-2.0/envs/Platform/Resourcegroups
ARM_CLIENT_ID: 
ARM_CLIENT_SECRET: 
ARM_TENANT_ID: 
ARM_SUBSCRIPTION_ID: fce95a24-209c-4e69-98de-885660170e1d
Processing in directory: /mnt/c/Users/<USER>/OneDrive - DynaTech Systems Pvt. Ltd/Desktop/Azure-Landing-Zone/Azure2.0/AACN/aacn-azure/azure-2.0/envs/Platform/Resourcegroups
Deleting .terraform directory to clean up stale state file, modules and plugins so to avoid Azure init errors
Running: terraform init
[0m[1mInitializing the backend...[0m
[0m[32m
Successfully configured the backend "azurerm"! Terraform will automatically
use this backend unless the backend configuration changes.[0m
[0m[1mInitializing modules...[0m
- cloudpc in ../Resourcegroups/Import/rg-platform-cloudpc-westus2-001
- cloudpc.rg-platform-cloudpc-westus2-001 in ../../../modules/resource_group
- comms in ../Resourcegroups/Import/rg-platform-comms-westus2-001
- comms.rg-platform-comms-westus2-001 in ../../../modules/resource_group
- connectivity in ../Resourcegroups/Import/rg-platform-connectivity-westus2-001
- connectivity.frontdoor in ../../../modules/frontdoor
- connectivity.hub_network in ../../../modules/networking/hub_vnet
- connectivity.nsg_bastion in ../../../modules/networking/nsg
- connectivity.nsg_cloudpcs_test in ../../../modules/networking/nsg
- connectivity.nsg_dns_inbound in ../../../modules/networking/nsg
- connectivity.nsg_dns_outbound in ../../../modules/networking/nsg
- connectivity.nsg_identity_dc in ../../../modules/networking/nsg
- connectivity.nsg_privatelink_storage in ../../../modules/networking/nsg
- connectivity.rg-platform-connectivity-westus2-001 in ../../../modules/resource_group
- dept in ../Resourcegroups/Import/rg-platform-users-westus2-001-rg
- dept.dept_storage in ../../../modules/storage_account/st-nocontainer
- dept.rg-platform-users-westus2-001-rg in ../../../modules/resource_group
- dept.users_storage in ../../../modules/storage_account/st-nocontainer
- devops in ../Resourcegroups/Import/rg-platform-devops-centralus-001
- devops.rg-platform-devops-centralus-001 in ../../../modules/resource_group
- honeypot in ../Resourcegroups/Import/rg-platform-honeypot-westus2-001
- identity in ../Resourcegroups/Import/rg-platform-identity-westus2-001
- identity.aadds_network in ../../../modules/networking/aadds
- identity.key_vaults in ../../../modules/key_vaults
- identity.rg-platform-identity-westus2-001 in ../../../modules/resource_group
- keyvaultmanager in ../Resourcegroups/Import/rg-platform-keyvaultmanager-westus2-001
- keyvaultmanager.keyvault_storage in ../../../modules/storage_account
- keyvaultmanager.rg-platform-keyvaultmanager-westus2-001 in ../../../modules/resource_group
- mgmt in ../Resourcegroups/Import/rg-platform-mgmt-westus2-001
- mgmt.key_vaults in ../../../modules/key_vaults
- mgmt.platform_storage in ../../../modules/storage_account
- mgmt.rg-platform-mgmt-westus2-001 in ../../../modules/resource_group
- sent in ../Resourcegroups/Import/rg-platform-snet-westus2-001
- sent.rg-platform-snet-westus2-001 in ../../../modules/resource_group
- sent.sentinel_storage in ../../../modules/storage_account
- testvm in Import/vm-platform-testvm-001
[0m[1mInitializing provider plugins...[0m
- terraform.io/builtin/terraform is built in to Terraform
- Reusing previous version of hashicorp/azurerm from the dependency lock file
- Installing hashicorp/azurerm v3.112.0...
- Installed hashicorp/azurerm v3.112.0 (signed by HashiCorp)

[0m[1m[32mTerraform has been successfully initialized![0m[32m[0m
[0m[32m
You may now begin working with Terraform. Try running "terraform plan" to see
any changes that are required for your infrastructure. All Terraform commands
should now work.

If you ever set or change modules or backend configuration for Terraform,
rerun this command to reinitialize your working directory. If you forget, other
commands will detect it and remind you to do so if necessary.[0m
Running: terraform fmt
Running: terraform validate
[32m[1mSuccess![0m The configuration is valid.
[0m
Running: terraform plan 
Acquiring state lock. This may take a few moments...
[0m[1mmodule.connectivity.data.terraform_remote_state.spoke2: Reading...[0m[0m
[0m[1mmodule.connectivity.data.terraform_remote_state.spoke2: Still reading... [00m10s elapsed][0m[0m
[0m[1mmodule.connectivity.data.terraform_remote_state.spoke2: Read complete after 18s[0m
[0m[1mmodule.connectivity.data.terraform_remote_state.spoke1: Reading...[0m[0m
[0m[1mmodule.connectivity.data.terraform_remote_state.spoke1: Still reading... [00m10s elapsed][0m[0m
[0m[1mmodule.connectivity.data.terraform_remote_state.spoke1: Read complete after 17s[0m
[0m[1mmodule.connectivity.data.azurerm_virtual_network.hub_vnet: Reading...[0m[0m
[0m[1mmodule.connectivity.azurerm_virtual_network_peering.hub_to_spoke2: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/virtualNetworkPeerings/hub-to-dev][0m
[0m[1mmodule.connectivity.data.azurerm_virtual_network.hub_vnet: Read complete after 1s [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001][0m
[0m[1mmodule.connectivity.module.frontdoor.azurerm_cdn_frontdoor_profile.this: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Cdn/profiles/afd-platform-hub-westus2-001][0m
[0m[1mmodule.connectivity.azurerm_private_dns_zone.aacn_org: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/privateDnsZones/aacn.org][0m
[0m[1mmodule.connectivity.azurerm_virtual_network_peering.hub_to_spoke1: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/virtualNetworkPeerings/hub-to-prod][0m
[0m[1mmodule.connectivity.azurerm_virtual_network_peering.hub_to_iland_spoke: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/virtualNetworkPeerings/hub_to_iland_spoke][0m
[0m[1mmodule.connectivity.azurerm_public_ip.vpn_gateway_ip: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/publicIPAddresses/pip-gw-vnet-platform-hub-westus2-001][0m
[0m[1mmodule.connectivity.module.rg-platform-connectivity-westus2-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001][0m
[0m[1mmodule.connectivity.azurerm_local_network_gateway.on_prem: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/localNetworkGateways/gw-l-platform-hub-vpn-hq-westus2-001][0m
[0m[1mmodule.connectivity.azurerm_subnet.gateway_subnet: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/GatewaySubnet][0m
[0m[1mmodule.connectivity.module.hub_network.azurerm_virtual_network.hub_vnet: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001][0m
[0m[1mmodule.connectivity.module.frontdoor.azurerm_cdn_frontdoor_endpoint.this: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Cdn/profiles/afd-platform-hub-westus2-001/afdEndpoints/afdend-wwwaacnorg-dev][0m
[0m[1mmodule.connectivity.module.frontdoor.azurerm_cdn_frontdoor_origin_group.this: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Cdn/profiles/afd-platform-hub-westus2-001/originGroups/afdpool-wwwaacnorg-dev][0m
[0m[1mmodule.connectivity.azurerm_virtual_network_gateway.vpn_gateway: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworkGateways/gw-v-vnet-platform-hub-westus2-001][0m
[0m[1mmodule.connectivity.module.hub_network.azurerm_subnet.hub_subnet: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/snet-identity-dc][0m
[0m[1mmodule.comms.azurerm_communication_service.acs: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-comms-westus2-001/providers/Microsoft.Communication/communicationServices/acs-platform-comms-001][0m
[0m[1mmodule.comms.azurerm_email_communication_service.email_service: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-comms-westus2-001/providers/Microsoft.Communication/emailServices/acssmtp-platform-comms-001][0m
[0m[1mmodule.comms.module.rg-platform-comms-westus2-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-comms-westus2-001][0m
[0m[1mmodule.connectivity.module.frontdoor.azurerm_cdn_frontdoor_origin.this: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Cdn/profiles/afd-platform-hub-westus2-001/originGroups/afdpool-wwwaacnorg-dev/origins/afdpool-wwwaacnorg-dev-origin][0m
[0m[1mmodule.connectivity.azurerm_virtual_network_gateway_connection.vpn_connection: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/connections/cn-gw-l-platform-westus2-001-to-gw-v-production-westus2-001][0m
[0m[1mmodule.comms.azurerm_email_communication_service_domain.email_domain: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-comms-westus2-001/providers/Microsoft.Communication/emailServices/acssmtp-platform-comms-001/domains/aacn.org][0m
[0m[1mmodule.devops.module.rg-platform-devops-centralus-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-devops-centralus-001][0m
[0m[1mmodule.connectivity.module.frontdoor.azurerm_cdn_frontdoor_route.this: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Cdn/profiles/afd-platform-hub-westus2-001/afdEndpoints/afdend-wwwaacnorg-dev/routes/default-route][0m
[0m[1mmodule.mgmt.module.rg-platform-mgmt-westus2-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-mgmt-westus2-001][0m
[0m[1mmodule.mgmt.module.key_vaults.azurerm_key_vault.key_vault["kv-platform-terra-001"]: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-mgmt-westus2-001/providers/Microsoft.KeyVault/vaults/kv-platform-terra-001][0m
[0m[1mmodule.mgmt.module.platform_storage.azurerm_storage_account.storage: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-mgmt-westus2-001/providers/Microsoft.Storage/storageAccounts/stplatformterra001][0m
[0m[1mmodule.keyvaultmanager.module.rg-platform-keyvaultmanager-westus2-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-keyvaultmanager-westus2-001][0m
[0m[1mmodule.keyvaultmanager.module.keyvault_storage.azurerm_storage_account.storage: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-keyvaultmanager-westus2-001/providers/Microsoft.Storage/storageAccounts/stkeyvaultmanager001][0m
[0m[1mmodule.sent.module.sentinel_storage.azurerm_storage_account.storage: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-sent-westus2-001/providers/Microsoft.Storage/storageAccounts/stplatsent001][0m
[0m[1mmodule.sent.azurerm_log_analytics_workspace.log_analytics: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-sent-westus2-001/providers/Microsoft.OperationalInsights/workspaces/work-platform-sent-001][0m
[0m[1mmodule.sent.module.rg-platform-snet-westus2-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-sent-westus2-001][0m
[0m[1mmodule.cloudpc.module.rg-platform-cloudpc-westus2-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-cloudpc-westus2-001][0m
[0m[1mmodule.identity.module.key_vaults.azurerm_key_vault.key_vault["kv-identity-westus2-001"]: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-identity-westus2-001/providers/Microsoft.KeyVault/vaults/kv-identity-westus2-001][0m
[0m[1mmodule.identity.module.rg-platform-identity-westus2-001.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-identity-westus2-001][0m
[0m[1mmodule.dept.module.dept_storage.azurerm_storage_account.storage: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-users-westus2-001/providers/Microsoft.Storage/storageAccounts/stplatformdept001][0m
[0m[1mmodule.dept.module.users_storage.azurerm_storage_account.storage: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-users-westus2-001/providers/Microsoft.Storage/storageAccounts/stplatformusers001][0m
[0m[1mmodule.dept.module.rg-platform-users-westus2-001-rg.azurerm_resource_group.rg: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-users-westus2-001][0m
[0m[1mmodule.sent.azurerm_sentinel_log_analytics_workspace_onboarding.sentinel: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-sent-westus2-001/providers/Microsoft.OperationalInsights/workspaces/work-platform-sent-001/providers/Microsoft.SecurityInsights/onboardingStates/default][0m
[0m[1mmodule.mgmt.module.key_vaults.azurerm_key_vault_access_policy.access_policy["1"]: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-mgmt-westus2-001/providers/Microsoft.KeyVault/vaults/kv-platform-terra-001/objectId/485da724-018a-4d86-8aee-adf46326de2f][0m
[0m[1mmodule.mgmt.module.key_vaults.azurerm_key_vault_access_policy.access_policy["0"]: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-mgmt-westus2-001/providers/Microsoft.KeyVault/vaults/kv-platform-terra-001/objectId/5866b75a-2207-4212-ad24-3b725610b2a1][0m
[0m[1mmodule.mgmt.module.key_vaults.azurerm_key_vault_access_policy.access_policy["2"]: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-mgmt-westus2-001/providers/Microsoft.KeyVault/vaults/kv-platform-terra-001/objectId/45f280a8-4460-4ac1-bcbd-ca26d65a679e][0m
[0m[1mmodule.identity.module.key_vaults.azurerm_key_vault_access_policy.access_policy["1"]: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-identity-westus2-001/providers/Microsoft.KeyVault/vaults/kv-identity-westus2-001/objectId/485da724-018a-4d86-8aee-adf46326de2f][0m
[0m[1mmodule.identity.module.key_vaults.azurerm_key_vault_access_policy.access_policy["2"]: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-identity-westus2-001/providers/Microsoft.KeyVault/vaults/kv-identity-westus2-001/objectId/45f280a8-4460-4ac1-bcbd-ca26d65a679e][0m
[0m[1mmodule.identity.module.key_vaults.azurerm_key_vault_access_policy.access_policy["0"]: Refreshing state... [id=/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-identity-westus2-001/providers/Microsoft.KeyVault/vaults/kv-identity-westus2-001/objectId/5866b75a-2207-4212-ad24-3b725610b2a1][0m
[0m[1mmodule.keyvaultmanager.module.keyvault_storage.azurerm_storage_container.tfstate: Refreshing state... [id=https://stkeyvaultmanager001.blob.core.windows.net/keyvaultartifact][0m
[0m[1mmodule.mgmt.module.platform_storage.azurerm_storage_container.tfstate: Refreshing state... [id=https://stplatformterra001.blob.core.windows.net/tfstate][0m
[0m[1mmodule.sent.module.sentinel_storage.azurerm_storage_container.tfstate: Refreshing state... [id=https://stplatsent001.blob.core.windows.net/sentlogs][0m

Terraform used the selected providers to generate the following execution
plan. Resource actions are indicated with the following symbols:
  [32m+[0m create[0m
  [33m~[0m update in-place[0m

Terraform will perform the following actions:

[1m  # module.connectivity.azurerm_bastion_host.bastion[0m will be created
[0m  [32m+[0m[0m resource "azurerm_bastion_host" "bastion" {
      [32m+[0m[0m copy_paste_enabled     = true
      [32m+[0m[0m dns_name               = (known after apply)
      [32m+[0m[0m file_copy_enabled      = false
      [32m+[0m[0m id                     = (known after apply)
      [32m+[0m[0m ip_connect_enabled     = false
      [32m+[0m[0m kerberos_enabled       = false
      [32m+[0m[0m location               = "westus2"
      [32m+[0m[0m name                   = "bast-platform-hub-westus2-001"
      [32m+[0m[0m resource_group_name    = "rg-platform-connectivity-westus2-001"
      [32m+[0m[0m scale_units            = 2
      [32m+[0m[0m shareable_link_enabled = false
      [32m+[0m[0m sku                    = "Standard"
      [32m+[0m[0m tags                   = {
          [32m+[0m[0m "CostCenter"  = "1911-51210"
          [32m+[0m[0m "Environment" = "prod"
        }
      [32m+[0m[0m tunneling_enabled      = false

      [32m+[0m[0m ip_configuration {
          [32m+[0m[0m name                 = "IpConf"
          [32m+[0m[0m public_ip_address_id = (known after apply)
          [32m+[0m[0m subnet_id            = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/AzureBastionSubnet"
        }
    }

[1m  # module.connectivity.azurerm_private_dns_resolver.resolver[0m will be created
[0m  [32m+[0m[0m resource "azurerm_private_dns_resolver" "resolver" {
      [32m+[0m[0m id                  = (known after apply)
      [32m+[0m[0m location            = "westus2"
      [32m+[0m[0m name                = "dnspr-platform-aacnorg-001"
      [32m+[0m[0m resource_group_name = "rg-platform-connectivity-westus2-001"
      [32m+[0m[0m tags                = {
          [32m+[0m[0m "CostCenter"  = "1911-51210"
          [32m+[0m[0m "Environment" = "prod"
        }
      [32m+[0m[0m virtual_network_id  = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001"
    }

[1m  # module.connectivity.azurerm_private_dns_resolver_dns_forwarding_ruleset.ruleset[0m will be created
[0m  [32m+[0m[0m resource "azurerm_private_dns_resolver_dns_forwarding_ruleset" "ruleset" {
      [32m+[0m[0m id                                         = (known after apply)
      [32m+[0m[0m location                                   = "westus2"
      [32m+[0m[0m name                                       = "dnsprend-ruleset-aacnorg"
      [32m+[0m[0m private_dns_resolver_outbound_endpoint_ids = (known after apply)
      [32m+[0m[0m resource_group_name                        = "rg-platform-connectivity-westus2-001"
      [32m+[0m[0m tags                                       = {
          [32m+[0m[0m "CostCenter"  = "1911-51210"
          [32m+[0m[0m "Environment" = "prod"
        }
    }

[1m  # module.connectivity.azurerm_private_dns_resolver_outbound_endpoint.outbound_endpoint[0m will be created
[0m  [32m+[0m[0m resource "azurerm_private_dns_resolver_outbound_endpoint" "outbound_endpoint" {
      [32m+[0m[0m id                      = (known after apply)
      [32m+[0m[0m location                = "westus2"
      [32m+[0m[0m name                    = "dnsprend-aacnorg-001"
      [32m+[0m[0m private_dns_resolver_id = (known after apply)
      [32m+[0m[0m subnet_id               = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/snet-dns-outbound"
      [32m+[0m[0m tags                    = {
          [32m+[0m[0m "CostCenter"  = "1911-51210"
          [32m+[0m[0m "Environment" = "prod"
        }
    }

[1m  # module.connectivity.azurerm_private_dns_zone.dns_zone[0m will be created
[0m  [32m+[0m[0m resource "azurerm_private_dns_zone" "dns_zone" {
      [32m+[0m[0m id                                                    = (known after apply)
      [32m+[0m[0m max_number_of_record_sets                             = (known after apply)
      [32m+[0m[0m max_number_of_virtual_network_links                   = (known after apply)
      [32m+[0m[0m max_number_of_virtual_network_links_with_registration = (known after apply)
      [32m+[0m[0m name                                                  = "aacn.org"
      [32m+[0m[0m number_of_record_sets                                 = (known after apply)
      [32m+[0m[0m resource_group_name                                   = "rg-platform-connectivity-westus2-001"
      [32m+[0m[0m tags                                                  = {
          [32m+[0m[0m "CostCenter"  = "1911-51210"
          [32m+[0m[0m "Environment" = "prod"
        }

      [32m+[0m[0m soa_record (known after apply)
    }

[1m  # module.connectivity.azurerm_private_dns_zone_virtual_network_link.vnet_link[0m will be created
[0m  [32m+[0m[0m resource "azurerm_private_dns_zone_virtual_network_link" "vnet_link" {
      [32m+[0m[0m id                    = (known after apply)
      [32m+[0m[0m name                  = "link-aacn.org-to-platform-hub-westus2-001"
      [32m+[0m[0m private_dns_zone_name = "aacn.org"
      [32m+[0m[0m registration_enabled  = true
      [32m+[0m[0m resource_group_name   = "rg-platform-connectivity-westus2-001"
      [32m+[0m[0m virtual_network_id    = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001"
    }

[1m  # module.connectivity.azurerm_public_ip.bastion_pip[0m will be created
[0m  [32m+[0m[0m resource "azurerm_public_ip" "bastion_pip" {
      [32m+[0m[0m allocation_method       = "Static"
      [32m+[0m[0m ddos_protection_mode    = "VirtualNetworkInherited"
      [32m+[0m[0m fqdn                    = (known after apply)
      [32m+[0m[0m id                      = (known after apply)
      [32m+[0m[0m idle_timeout_in_minutes = 4
      [32m+[0m[0m ip_address              = (known after apply)
      [32m+[0m[0m ip_version              = "IPv4"
      [32m+[0m[0m location                = "westus2"
      [32m+[0m[0m name                    = "pip-bast-platform-hub-westus2-001"
      [32m+[0m[0m resource_group_name     = "rg-platform-connectivity-westus2-001"
      [32m+[0m[0m sku                     = "Standard"
      [32m+[0m[0m sku_tier                = "Regional"
      [32m+[0m[0m tags                    = {
          [32m+[0m[0m "CostCenter"  = "1911-51210"
          [32m+[0m[0m "Environment" = "prod"
        }
      [32m+[0m[0m zones                   = [
          [32m+[0m[0m "1",
          [32m+[0m[0m "2",
          [32m+[0m[0m "3",
        ]
    }

[1m  # module.connectivity.module.nsg_bastion.azurerm_network_security_group.nsg[0m will be created
[0m  [32m+[0m[0m resource "azurerm_network_security_group" "nsg" {
      [32m+[0m[0m id                  = (known after apply)
      [32m+[0m[0m location            = "westus2"
      [32m+[0m[0m name                = "nsg-snet-bastion"
      [32m+[0m[0m resource_group_name = "rg-platform-connectivity-westus2-001"
      [32m+[0m[0m security_rule       = (known after apply)
      [32m+[0m[0m tags                = {
          [32m+[0m[0m "CostCenter"  = "1911-51210"
          [32m+[0m[0m "Environment" = "prod"
        }
    }

[1m  # module.connectivity.module.nsg_cloudpcs_test.azurerm_network_security_group.nsg[0m will be created
[0m  [32m+[0m[0m resource "azurerm_network_security_group" "nsg" {
      [32m+[0m[0m id                  = (known after apply)
      [32m+[0m[0m location            = "westus2"
      [32m+[0m[0m name                = "nsg-snet-cloudpcs-test"
      [32m+[0m[0m resource_group_name = "rg-platform-connectivity-westus2-001"
      [32m+[0m[0m security_rule       = (known after apply)
    }

[1m  # module.connectivity.module.nsg_dns_inbound.azurerm_network_security_group.nsg[0m will be created
[0m  [32m+[0m[0m resource "azurerm_network_security_group" "nsg" {
      [32m+[0m[0m id                  = (known after apply)
      [32m+[0m[0m location            = "westus2"
      [32m+[0m[0m name                = "nsg-snet-dns-inbound"
      [32m+[0m[0m resource_group_name = "rg-platform-connectivity-westus2-001"
      [32m+[0m[0m security_rule       = (known after apply)
      [32m+[0m[0m tags                = {
          [32m+[0m[0m "CostCenter"  = "1911-51210"
          [32m+[0m[0m "Environment" = "prod"
        }
    }

[1m  # module.connectivity.module.nsg_dns_outbound.azurerm_network_security_group.nsg[0m will be created
[0m  [32m+[0m[0m resource "azurerm_network_security_group" "nsg" {
      [32m+[0m[0m id                  = (known after apply)
      [32m+[0m[0m location            = "westus2"
      [32m+[0m[0m name                = "nsg-snet-dns-outbound"
      [32m+[0m[0m resource_group_name = "rg-platform-connectivity-westus2-001"
      [32m+[0m[0m security_rule       = (known after apply)
      [32m+[0m[0m tags                = {
          [32m+[0m[0m "CostCenter"  = "1911-51210"
          [32m+[0m[0m "Environment" = "prod"
        }
    }

[1m  # module.connectivity.module.nsg_identity_dc.azurerm_network_security_group.nsg[0m will be created
[0m  [32m+[0m[0m resource "azurerm_network_security_group" "nsg" {
      [32m+[0m[0m id                  = (known after apply)
      [32m+[0m[0m location            = "westus2"
      [32m+[0m[0m name                = "nsg-snet-identity-dc"
      [32m+[0m[0m resource_group_name = "rg-platform-connectivity-westus2-001"
      [32m+[0m[0m security_rule       = (known after apply)
      [32m+[0m[0m tags                = {
          [32m+[0m[0m "CostCenter"  = "1911-51210"
          [32m+[0m[0m "Environment" = "prod"
        }
    }

[1m  # module.connectivity.module.nsg_privatelink_storage.azurerm_network_security_group.nsg[0m will be created
[0m  [32m+[0m[0m resource "azurerm_network_security_group" "nsg" {
      [32m+[0m[0m id                  = (known after apply)
      [32m+[0m[0m location            = "westus2"
      [32m+[0m[0m name                = "nsg-snet-privatelink-storage"
      [32m+[0m[0m resource_group_name = "rg-platform-connectivity-westus2-001"
      [32m+[0m[0m security_rule       = (known after apply)
      [32m+[0m[0m tags                = {
          [32m+[0m[0m "CostCenter"  = "1911-51210"
          [32m+[0m[0m "Environment" = "prod"
        }
    }

[1m  # module.dept.module.dept_storage.azurerm_storage_account.storage[0m will be updated in-place
[0m  [33m~[0m[0m resource "azurerm_storage_account" "storage" {
        id                                = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-users-westus2-001/providers/Microsoft.Storage/storageAccounts/stplatformdept001"
        name                              = "stplatformdept001"
        tags                              = {
            "CostCenter"  = "1911-51210"
            "environment" = "Platform"
        }
        [90m# (63 unchanged attributes hidden)[0m[0m

      [31m-[0m[0m azure_files_authentication {
          [31m-[0m[0m directory_type = "AADDS" [90m-> null[0m[0m

          [31m-[0m[0m active_directory {
              [31m-[0m[0m domain_name         = "aacn.org" [90m-> null[0m[0m
                [90m# (5 unchanged attributes hidden)[0m[0m
            }
        }

        [90m# (4 unchanged blocks hidden)[0m[0m
    }

[1m  # module.identity.module.aadds_network.azurerm_lb.aadds_lb[0m will be created
[0m  [32m+[0m[0m resource "azurerm_lb" "aadds_lb" {
      [32m+[0m[0m id                   = (known after apply)
      [32m+[0m[0m location             = "westus2"
      [32m+[0m[0m name                 = "aadds-bfdf416bd26b4958a920d51094d43f5e-lb"
      [32m+[0m[0m private_ip_address   = (known after apply)
      [32m+[0m[0m private_ip_addresses = (known after apply)
      [32m+[0m[0m resource_group_name  = "rg-platform-identity-westus2-001"
      [32m+[0m[0m sku                  = "Basic"
      [32m+[0m[0m sku_tier             = "Regional"
      [32m+[0m[0m tags                 = {
          [32m+[0m[0m "Environment" = "Platform"
          [32m+[0m[0m "Service"     = "AADDS"
          [32m+[0m[0m "Terraform"   = "true"
        }

      [32m+[0m[0m frontend_ip_configuration {
          [32m+[0m[0m gateway_load_balancer_frontend_ip_configuration_id = (known after apply)
          [32m+[0m[0m id                                                 = (known after apply)
          [32m+[0m[0m inbound_nat_rules                                  = (known after apply)
          [32m+[0m[0m load_balancer_rules                                = (known after apply)
          [32m+[0m[0m name                                               = "LK-JWW7L0ZFMZB4Fe"
          [32m+[0m[0m outbound_rules                                     = (known after apply)
          [32m+[0m[0m private_ip_address                                 = (known after apply)
          [32m+[0m[0m private_ip_address_allocation                      = (known after apply)
          [32m+[0m[0m private_ip_address_version                         = (known after apply)
          [32m+[0m[0m public_ip_address_id                               = (known after apply)
          [32m+[0m[0m public_ip_prefix_id                                = (known after apply)
          [32m+[0m[0m subnet_id                                          = (known after apply)
        }
    }

[1m  # module.identity.module.aadds_network.azurerm_lb_backend_address_pool.aadds_backend_pool[0m will be created
[0m  [32m+[0m[0m resource "azurerm_lb_backend_address_pool" "aadds_backend_pool" {
      [32m+[0m[0m backend_ip_configurations = (known after apply)
      [32m+[0m[0m id                        = (known after apply)
      [32m+[0m[0m inbound_nat_rules         = (known after apply)
      [32m+[0m[0m load_balancing_rules      = (known after apply)
      [32m+[0m[0m loadbalancer_id           = (known after apply)
      [32m+[0m[0m name                      = "LK-JWW7L0ZFMZB4Be"
      [32m+[0m[0m outbound_rules            = (known after apply)
    }

[1m  # module.identity.module.aadds_network.azurerm_lb_nat_rule.aadds_nat_rule_1[0m will be created
[0m  [32m+[0m[0m resource "azurerm_lb_nat_rule" "aadds_nat_rule_1" {
      [32m+[0m[0m backend_ip_configuration_id    = (known after apply)
      [32m+[0m[0m backend_port                   = 5986
      [32m+[0m[0m enable_floating_ip             = (known after apply)
      [32m+[0m[0m frontend_ip_configuration_id   = (known after apply)
      [32m+[0m[0m frontend_ip_configuration_name = "LK-JWW7L0ZFMZB4Fe"
      [32m+[0m[0m frontend_port                  = 5986
      [32m+[0m[0m id                             = (known after apply)
      [32m+[0m[0m idle_timeout_in_minutes        = 15
      [32m+[0m[0m loadbalancer_id                = (known after apply)
      [32m+[0m[0m name                           = "GPXUMNTT4QWKXBVPsh"
      [32m+[0m[0m protocol                       = "Tcp"
      [32m+[0m[0m resource_group_name            = "rg-platform-identity-westus2-001"
    }

[1m  # module.identity.module.aadds_network.azurerm_lb_nat_rule.aadds_nat_rule_2[0m will be created
[0m  [32m+[0m[0m resource "azurerm_lb_nat_rule" "aadds_nat_rule_2" {
      [32m+[0m[0m backend_ip_configuration_id    = (known after apply)
      [32m+[0m[0m backend_port                   = 5986
      [32m+[0m[0m enable_floating_ip             = (known after apply)
      [32m+[0m[0m frontend_ip_configuration_id   = (known after apply)
      [32m+[0m[0m frontend_ip_configuration_name = "LK-JWW7L0ZFMZB4Fe"
      [32m+[0m[0m frontend_port                  = 5987
      [32m+[0m[0m id                             = (known after apply)
      [32m+[0m[0m idle_timeout_in_minutes        = 15
      [32m+[0m[0m loadbalancer_id                = (known after apply)
      [32m+[0m[0m name                           = "W-93GOMVYO3AONGsh"
      [32m+[0m[0m protocol                       = "Tcp"
      [32m+[0m[0m resource_group_name            = "rg-platform-identity-westus2-001"
    }

[1m  # module.identity.module.aadds_network.azurerm_network_interface.aadds_nic_1[0m will be created
[0m  [32m+[0m[0m resource "azurerm_network_interface" "aadds_nic_1" {
      [32m+[0m[0m accelerated_networking_enabled = (known after apply)
      [32m+[0m[0m applied_dns_servers            = (known after apply)
      [32m+[0m[0m dns_servers                    = [
          [32m+[0m[0m "********",
          [32m+[0m[0m "********",
        ]
      [32m+[0m[0m enable_accelerated_networking  = (known after apply)
      [32m+[0m[0m enable_ip_forwarding           = (known after apply)
      [32m+[0m[0m id                             = (known after apply)
      [32m+[0m[0m internal_domain_name_suffix    = (known after apply)
      [32m+[0m[0m ip_forwarding_enabled          = (known after apply)
      [32m+[0m[0m location                       = "westus2"
      [32m+[0m[0m mac_address                    = (known after apply)
      [32m+[0m[0m name                           = "aadds-82010bb7eb544148ab08871742852500-nic"
      [32m+[0m[0m private_ip_address             = (known after apply)
      [32m+[0m[0m private_ip_addresses           = (known after apply)
      [32m+[0m[0m resource_group_name            = "rg-platform-identity-westus2-001"
      [32m+[0m[0m tags                           = {
          [32m+[0m[0m "Environment" = "Platform"
          [32m+[0m[0m "Service"     = "AADDS"
          [32m+[0m[0m "Terraform"   = "true"
        }
      [32m+[0m[0m virtual_machine_id             = (known after apply)

      [32m+[0m[0m ip_configuration {
          [32m+[0m[0m gateway_load_balancer_frontend_ip_configuration_id = (known after apply)
          [32m+[0m[0m name                                               = "GPXUMNTT4QWKXBVIpcfg"
          [32m+[0m[0m primary                                            = (known after apply)
          [32m+[0m[0m private_ip_address                                 = (known after apply)
          [32m+[0m[0m private_ip_address_allocation                      = "Dynamic"
          [32m+[0m[0m private_ip_address_version                         = "IPv4"
          [32m+[0m[0m subnet_id                                          = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/snet-identity-dc"
        }
    }

[1m  # module.identity.module.aadds_network.azurerm_network_interface.aadds_nic_2[0m will be created
[0m  [32m+[0m[0m resource "azurerm_network_interface" "aadds_nic_2" {
      [32m+[0m[0m accelerated_networking_enabled = (known after apply)
      [32m+[0m[0m applied_dns_servers            = (known after apply)
      [32m+[0m[0m dns_servers                    = [
          [32m+[0m[0m "********",
          [32m+[0m[0m "********",
        ]
      [32m+[0m[0m enable_accelerated_networking  = (known after apply)
      [32m+[0m[0m enable_ip_forwarding           = (known after apply)
      [32m+[0m[0m id                             = (known after apply)
      [32m+[0m[0m internal_domain_name_suffix    = (known after apply)
      [32m+[0m[0m ip_forwarding_enabled          = (known after apply)
      [32m+[0m[0m location                       = "westus2"
      [32m+[0m[0m mac_address                    = (known after apply)
      [32m+[0m[0m name                           = "aadds-cef2c9ec4658447bad45f74ce45ef0fe-nic"
      [32m+[0m[0m private_ip_address             = (known after apply)
      [32m+[0m[0m private_ip_addresses           = (known after apply)
      [32m+[0m[0m resource_group_name            = "rg-platform-identity-westus2-001"
      [32m+[0m[0m tags                           = {
          [32m+[0m[0m "Environment" = "Platform"
          [32m+[0m[0m "Service"     = "AADDS"
          [32m+[0m[0m "Terraform"   = "true"
        }
      [32m+[0m[0m virtual_machine_id             = (known after apply)

      [32m+[0m[0m ip_configuration {
          [32m+[0m[0m gateway_load_balancer_frontend_ip_configuration_id = (known after apply)
          [32m+[0m[0m name                                               = "W-93GOMVYO3AONGIpcfg"
          [32m+[0m[0m primary                                            = (known after apply)
          [32m+[0m[0m private_ip_address                                 = (known after apply)
          [32m+[0m[0m private_ip_address_allocation                      = "Dynamic"
          [32m+[0m[0m private_ip_address_version                         = "IPv4"
          [32m+[0m[0m subnet_id                                          = "/subscriptions/fce95a24-209c-4e69-98de-885660170e1d/resourceGroups/rg-platform-connectivity-westus2-001/providers/Microsoft.Network/virtualNetworks/vnet-platform-hub-westus2-001/subnets/snet-identity-dc"
        }
    }

[1m  # module.identity.module.aadds_network.azurerm_network_interface_backend_address_pool_association.aadds_nic_1_pool[0m will be created
[0m  [32m+[0m[0m resource "azurerm_network_interface_backend_address_pool_association" "aadds_nic_1_pool" {
      [32m+[0m[0m backend_address_pool_id = (known after apply)
      [32m+[0m[0m id                      = (known after apply)
      [32m+[0m[0m ip_configuration_name   = "GPXUMNTT4QWKXBVIpcfg"
      [32m+[0m[0m network_interface_id    = (known after apply)
    }

[1m  # module.identity.module.aadds_network.azurerm_network_interface_backend_address_pool_association.aadds_nic_2_pool[0m will be created
[0m  [32m+[0m[0m resource "azurerm_network_interface_backend_address_pool_association" "aadds_nic_2_pool" {
      [32m+[0m[0m backend_address_pool_id = (known after apply)
      [32m+[0m[0m id                      = (known after apply)
      [32m+[0m[0m ip_configuration_name   = "W-93GOMVYO3AONGIpcfg"
      [32m+[0m[0m network_interface_id    = (known after apply)
    }

[1m  # module.identity.module.aadds_network.azurerm_network_interface_nat_rule_association.aadds_nic_1_nat[0m will be created
[0m  [32m+[0m[0m resource "azurerm_network_interface_nat_rule_association" "aadds_nic_1_nat" {
      [32m+[0m[0m id                    = (known after apply)
      [32m+[0m[0m ip_configuration_name = "GPXUMNTT4QWKXBVIpcfg"
      [32m+[0m[0m nat_rule_id           = (known after apply)
      [32m+[0m[0m network_interface_id  = (known after apply)
    }

[1m  # module.identity.module.aadds_network.azurerm_network_interface_nat_rule_association.aadds_nic_2_nat[0m will be created
[0m  [32m+[0m[0m resource "azurerm_network_interface_nat_rule_association" "aadds_nic_2_nat" {
      [32m+[0m[0m id                    = (known after apply)
      [32m+[0m[0m ip_configuration_name = "W-93GOMVYO3AONGIpcfg"
      [32m+[0m[0m nat_rule_id           = (known after apply)
      [32m+[0m[0m network_interface_id  = (known after apply)
    }

[1m  # module.identity.module.aadds_network.azurerm_public_ip.aadds_pip[0m will be created
[0m  [32m+[0m[0m resource "azurerm_public_ip" "aadds_pip" {
      [32m+[0m[0m allocation_method       = "Static"
      [32m+[0m[0m ddos_protection_mode    = "VirtualNetworkInherited"
      [32m+[0m[0m domain_name_label       = "cust-a-a-c-n-0-o-r-g-d4d95bee-ce46-4803-b7e2-63d06658ef0b"
      [32m+[0m[0m fqdn                    = (known after apply)
      [32m+[0m[0m id                      = (known after apply)
      [32m+[0m[0m idle_timeout_in_minutes = 4
      [32m+[0m[0m ip_address              = (known after apply)
      [32m+[0m[0m ip_version              = "IPv4"
      [32m+[0m[0m location                = "westus2"
      [32m+[0m[0m name                    = "aadds-bfdf416bd26b4958a920d51094d43f5e-pip"
      [32m+[0m[0m resource_group_name     = "rg-platform-identity-westus2-001"
      [32m+[0m[0m sku                     = "Basic"
      [32m+[0m[0m sku_tier                = "Regional"
      [32m+[0m[0m tags                    = {
          [32m+[0m[0m "Environment" = "Platform"
          [32m+[0m[0m "Service"     = "AADDS"
          [32m+[0m[0m "Terraform"   = "true"
        }
    }

[1mPlan:[0m 24 to add, 1 to change, 0 to destroy.
[0m[90m
─────────────────────────────────────────────────────────────────────────────[0m

Note: You didn't use the -out option to save this plan, so Terraform can't
guarantee to take exactly these actions if you run "terraform apply" now.
Releasing state lock. This may take a few moments...
