# AACN Azure 2.0
## How to use run.sh
Usage: D:\Users\rgood\source\architecture\aacn-azure\azure-2.0\scripts\run.sh -e ENVIRONMENT -b BASE_PATH [-g RESOURCE_GROUP] COMMAND
  -e ENVIRONMENT      The environment to target (dev, prod, Platform)
  -b BASE_PATH        The base path to set in the script (relative to the script's directory)
  -g RESOURCE_GROUP   The resource group to target (optional)
  COMMAND             The terraform command to run (plan, apply --auto-approve, destroy --auto-approve, etc.)

Example
Platform Subscription
```
./run.sh -e Platform -b ../envs plan
```
DevTest Subscription
```
./run.sh -e dev -b ../envs plan
```
Production Subscription
```
./run.sh -e production -b ../envs plan
```

To list the state
./your_script.sh -e dev -b ../envs state list

To remove a specific resource from the state:
./your_script.sh -e dev -b ../envs state rm <resource_name>

To move a resource in the state:
./your_script.sh -e dev -b ../envs state mv <source> <destination>

To import a resource:
./your_script.sh -e dev -b ../envs import <address> <id>

