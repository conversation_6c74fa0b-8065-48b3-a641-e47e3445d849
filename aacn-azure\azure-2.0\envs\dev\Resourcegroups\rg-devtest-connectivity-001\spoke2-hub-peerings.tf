# spoke2-hub-peerings.tf (in aacn-devtest-001 subscription)
resource "azurerm_virtual_network_peering" "spoke2_to_hub" {
#   provider                  = azurerm.devtest
  name                      = "dev-to-hub"
  resource_group_name       = var.resource_group_name
  virtual_network_name      = "vnet-westus2-devtest-dev"
  remote_virtual_network_id = data.terraform_remote_state.hub.outputs.hub_vnet_id
  allow_virtual_network_access = true
}


data "terraform_remote_state" "hub" {
  backend = "azurerm"
  config = {
    storage_account_name = "stplatformterra001" 
    container_name       = "tfstate"               
    key                  = "platform.terraform.tfstate" 
    resource_group_name  = "rg-platform-mgmt-westus2-001"             
    subscription_id      = "fce95a24-209c-4e69-98de-885660170e1d"
  }
}
