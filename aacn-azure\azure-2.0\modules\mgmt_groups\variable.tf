variable "root_management_group_name" {
  description = "The name of the root management group"
  type        = string
}

variable "root_management_group_display_name" {
  description = "The display name of the root management group"
  type        = string
}

variable "landing_zones_management_group_name" {
  description = "The name of the landing zones management group"
  type        = string
}

variable "landing_zones_management_group_display_name" {
  description = "The display name of the landing zones management group"
  type        = string
}

variable "platform_management_group_name" {
  description = "The name of the platform management group"
  type        = string
}

variable "platform_management_group_display_name" {
  description = "The display name of the platform management group"
  type        = string
}

variable "sandbox_management_group_name" {
  description = "The name of the sandbox management group"
  type        = string
}

variable "sandbox_management_group_display_name" {
  description = "The display name of the sandbox management group"
  type        = string
}

# variable "subscription_id" {
#   description = "The subscription ID to be assigned to the management group"
#   type        = string
# }

variable "sandbox_subscription_id" {
  description = "Subscription ID for Sandbox"
  type        = set(string)
}

variable "platform_subscription_id" {
  description = "Subscription ID for Platform"
  type        = set(string)
}

variable "landing_zone_subscription_id" {
  description = "Subscription ID for Landing Zone"
  type        = set(string)
}